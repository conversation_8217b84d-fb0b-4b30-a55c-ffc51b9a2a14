#!/usr/bin/env python3
"""
Deep Quantum Training Validation
Rigorous testing of quantum training claims
"""

import sys
import time
import numpy as np
import polars as pl
from pathlib import Path

sys.path.append('.')

def test_quantum_training_with_real_optimizers():
    """Test quantum training with SPSA and COBYLA optimizers"""
    print("🔍 DEEP QUANTUM TRAINING VALIDATION")
    print("=" * 60)
    
    try:
        from app.agents.quantum.enhanced_training_pipeline import EnhancedQuantumTrainer, TrainingConfig, OptimizerType
        
        # Create proper training data with correct types
        training_data = {
            'symbol': ['BTC', 'ETH', 'ADA', 'DOT', 'LINK', 'UNI', 'AAVE', 'MATIC'],
            'price': [50000.0, 3000.0, 1.5, 25.0, 15.0, 8.0, 200.0, 0.8],
            'volume_24h': [1e9, 5e8, 1e8, 5e7, 1e8, 2e8, 1e7, 3e8],
            'price_change_percentage_24h': [2.5, -1.0, 5.0, -3.0, 1.5, 4.0, -2.0, 6.0],
            'market_cap': [1e12, 3e11, 5e10, 2e10, 6e9, 4e9, 8e9, 1e10],
            'market_cap_rank': [1, 2, 3, 4, 5, 6, 7, 8]
        }
        
        df = pl.DataFrame(training_data, strict=False)
        
        # Split data properly
        train_size = 6
        val_size = 1
        test_size = 1
        
        train_data = df.head(train_size)
        val_data = df.slice(train_size, val_size)
        test_data = df.tail(test_size)
        
        print(f"📊 Training samples: {train_data.height}")
        print(f"📊 Validation samples: {val_data.height}")
        print(f"📊 Test samples: {test_data.height}")
        
        # Test different optimizers
        optimizers_to_test = [
            (OptimizerType.SPSA, "SPSA"),
            (OptimizerType.COBYLA, "COBYLA")
        ]
        
        results = {}
        
        for optimizer_type, optimizer_name in optimizers_to_test:
            print(f"\n🚀 Testing {optimizer_name} optimizer...")
            
            config = TrainingConfig(
                max_iterations=10,
                optimizer_type=optimizer_type,
                num_qubits=4,
                num_layers=2,
                early_stopping_patience=5,
                learning_rate=0.1
            )
            
            trainer = EnhancedQuantumTrainer(config)
            
            start_time = time.time()
            
            try:
                result = trainer.train_model(
                    train_data=train_data,
                    val_data=val_data,
                    test_data=test_data,
                    model_name=f"test_{optimizer_name.lower()}"
                )
                
                training_time = time.time() - start_time
                
                print(f"✅ {optimizer_name} training completed")
                print(f"📊 Training time: {training_time:.2f}s")
                print(f"📊 Success: {result.success}")
                print(f"📊 Best validation cost: {result.best_val_cost:.4f}")
                print(f"📊 Total iterations: {result.total_iterations}")
                print(f"📊 Convergence: {result.convergence_achieved}")
                print(f"📊 Early stopped: {result.early_stopped}")
                print(f"📊 Overfitting detected: {result.overfitting_detected}")
                
                if result.best_parameters is not None:
                    print(f"📊 Parameters shape: {result.best_parameters.shape}")
                    print(f"📊 Parameter range: [{result.best_parameters.min():.4f}, {result.best_parameters.max():.4f}]")
                
                results[optimizer_name] = {
                    'success': result.success,
                    'training_time': training_time,
                    'best_val_cost': result.best_val_cost,
                    'iterations': result.total_iterations,
                    'convergence': result.convergence_achieved,
                    'parameters': result.best_parameters
                }
                
            except Exception as e:
                print(f"❌ {optimizer_name} training failed: {e}")
                results[optimizer_name] = {'success': False, 'error': str(e)}
        
        return results
        
    except Exception as e:
        print(f"❌ CRITICAL FAILURE: Quantum training validation failed: {e}")
        import traceback
        traceback.print_exc()
        return {}

def test_quantum_parameter_optimization():
    """Test if quantum parameters are actually being optimized"""
    print("\n🔍 QUANTUM PARAMETER OPTIMIZATION TEST")
    print("=" * 60)
    
    try:
        from app.agents.quantum.quantum_score_calculator import QuantumScoreCalculator
        
        calculator = QuantumScoreCalculator(num_qubits=4, num_layers=2)
        
        # Get initial parameters
        ansatz, observable = calculator.create_quantum_model()
        initial_params = np.random.uniform(0, 2*np.pi, ansatz.num_parameters)
        
        print(f"📊 Initial parameters: {initial_params}")
        
        # Test cost function with different parameters
        test_features = np.random.random((5, 4))  # 5 samples, 4 features
        test_targets = np.random.random(5)
        
        costs = []
        param_sets = []
        
        for i in range(5):
            # Generate different parameter sets
            params = np.random.uniform(0, 2*np.pi, ansatz.num_parameters)
            param_sets.append(params.copy())
            
            try:
                cost = calculator.quantum_cost_function(
                    parameters=params,
                    ansatz=ansatz,
                    observable=observable,
                    features=test_features,
                    targets=test_targets
                )
                costs.append(cost)
                print(f"📊 Parameter set {i+1}: cost = {cost:.4f}")
            except Exception as e:
                print(f"❌ Cost calculation failed for set {i+1}: {e}")
                costs.append(float('inf'))
        
        # Check if costs vary with parameters
        cost_variance = np.var(costs)
        print(f"📊 Cost variance across parameter sets: {cost_variance:.6f}")
        
        if cost_variance > 1e-6:
            print("✅ PASSED: Cost function varies with parameters")
            return True
        else:
            print("❌ FAILED: Cost function doesn't vary with parameters")
            return False
            
    except Exception as e:
        print(f"❌ CRITICAL FAILURE: Parameter optimization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_quantum_vs_classical_comparison():
    """Test if quantum vs classical comparison is meaningful"""
    print("\n🔍 QUANTUM VS CLASSICAL COMPARISON TEST")
    print("=" * 60)
    
    try:
        from app.agents.quantum.quantum_score_calculator import QuantumScoreCalculator
        
        # Create test data
        test_data = pl.DataFrame({
            'price': [100.0, 200.0, 50.0, 300.0, 150.0],
            'volume_24h': [1e6, 2e6, 5e5, 3e6, 1.5e6],
            'price_change_percentage_24h': [5.0, -2.0, 10.0, -5.0, 3.0],
            'market_cap': [1e9, 2e9, 5e8, 3e9, 1.5e9],
            'market_cap_rank': [1, 2, 3, 4, 5]
        })
        
        calculator = QuantumScoreCalculator()
        
        # Get quantum scores
        quantum_result = calculator.calculate_quantum_score(test_data)
        quantum_scores = quantum_result['quantum_scores']
        
        # Get classical scores
        classical_scores = calculator._calculate_classical_baseline(test_data)
        
        print("📊 Quantum vs Classical Scores:")
        for i, (q_score, c_score) in enumerate(zip(quantum_scores, classical_scores)):
            advantage = q_score - c_score
            print(f"  Sample {i+1}: Quantum={q_score:.4f}, Classical={c_score:.4f}, Advantage={advantage:.4f}")
        
        # Check if there's meaningful difference
        avg_advantage = np.mean(np.array(quantum_scores) - np.array(classical_scores))
        advantage_std = np.std(np.array(quantum_scores) - np.array(classical_scores))
        
        print(f"📊 Average quantum advantage: {avg_advantage:.4f}")
        print(f"📊 Advantage standard deviation: {advantage_std:.4f}")
        
        # Check if advantage is consistent and meaningful
        if abs(avg_advantage) > 0.01 and advantage_std < 0.5:
            print("✅ PASSED: Meaningful quantum vs classical difference")
            return True
        else:
            print("❌ FAILED: No meaningful quantum advantage detected")
            return False
            
    except Exception as e:
        print(f"❌ CRITICAL FAILURE: Quantum vs classical test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run deep quantum training validation"""
    print("🚀 DEEP QUANTUM TRAINING VALIDATION")
    print("=" * 80)
    
    # Test 1: Quantum training with real optimizers
    training_results = test_quantum_training_with_real_optimizers()
    
    # Test 2: Parameter optimization
    param_optimization_works = test_quantum_parameter_optimization()
    
    # Test 3: Quantum vs classical comparison
    quantum_advantage_meaningful = test_quantum_vs_classical_comparison()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 DEEP VALIDATION SUMMARY")
    print("=" * 80)
    
    training_success = any(result.get('success', False) for result in training_results.values())
    
    print(f"Quantum Training Success: {'✅ PASSED' if training_success else '❌ FAILED'}")
    print(f"Parameter Optimization: {'✅ PASSED' if param_optimization_works else '❌ FAILED'}")
    print(f"Quantum Advantage: {'✅ PASSED' if quantum_advantage_meaningful else '❌ FAILED'}")
    
    if training_results:
        print("\n📊 Training Results by Optimizer:")
        for optimizer, result in training_results.items():
            if result.get('success'):
                print(f"  {optimizer}: ✅ Success (cost: {result['best_val_cost']:.4f}, time: {result['training_time']:.2f}s)")
            else:
                print(f"  {optimizer}: ❌ Failed ({result.get('error', 'Unknown error')})")
    
    overall_success = training_success and param_optimization_works and quantum_advantage_meaningful
    
    if overall_success:
        print("\n🎉 DEEP VALIDATION PASSED")
        print("✅ System demonstrates genuine quantum training functionality")
    else:
        print("\n⚠️  DEEP VALIDATION FAILED")
        print("❌ System has significant quantum functionality issues")
    
    return overall_success

if __name__ == "__main__":
    main()
