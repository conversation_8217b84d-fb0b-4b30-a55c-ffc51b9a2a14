"""
Enhanced multi-source cryptocurrency data fetcher.
Supports CoinGecko, CCXT, and YFinance APIs with unified interface.
"""

import asyncio
import time
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import structlog
import polars as pl
import numpy as np

try:
    import ccxt
    import ccxt.async_support as ccxt_async
    CCXT_AVAILABLE = True
except ImportError:
    CCXT_AVAILABLE = False
    ccxt = None
    ccxt_async = None

try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False
    yf = None

from .coingecko_scraper import CoinGeckoScraper
from ...core.config import settings

logger = structlog.get_logger(__name__)


class DataSource(Enum):
    """Supported data sources."""
    COINGECKO = "coingecko"
    CCXT = "ccxt"
    YFINANCE = "yfinance"


@dataclass
class DataFetchConfig:
    """Configuration for data fetching."""
    sources: List[DataSource]
    symbols: List[str]
    limit: int = 50
    fallback_enabled: bool = True
    include_historical: bool = False
    historical_days: int = 30
    fallback_enabled: bool = True
    timeout_seconds: int = 30


@dataclass
class MarketDataPoint:
    """Standardized market data point."""
    symbol: str
    price: float
    price_change_24h: float
    price_change_percentage_24h: float
    volume_24h: float
    market_cap: Optional[float]
    market_cap_rank: Optional[int]
    circulating_supply: Optional[float]
    total_supply: Optional[float]
    volume_to_market_cap_ratio: float
    timestamp: datetime
    source: DataSource


class MultiSourceDataFetcher:
    """Enhanced multi-source cryptocurrency data fetcher."""
    
    def __init__(self):
        """Initialize the multi-source data fetcher."""
        self.logger = logger.bind(component="multi_source_fetcher")
        
        # Initialize data sources
        self.coingecko_scraper = CoinGeckoScraper()
        self.ccxt_exchanges = {}
        self.yfinance_available = YFINANCE_AVAILABLE
        
        # Initialize CCXT exchanges if available
        if CCXT_AVAILABLE:
            self._init_ccxt_exchanges()
        
        # Cache for data deduplication
        self._data_cache = {}
        self._cache_ttl = 300  # 5 minutes
        
        self.logger.info(
            "Multi-source data fetcher initialized",
            coingecko_available=True,
            ccxt_available=CCXT_AVAILABLE,
            yfinance_available=YFINANCE_AVAILABLE
        )
    
    def _init_ccxt_exchanges(self):
        """Initialize CCXT exchanges."""
        if not CCXT_AVAILABLE:
            return
        
        # Initialize major exchanges
        exchange_configs = [
            ('binance', {}),
            ('coinbase', {}),
            ('kraken', {}),
            ('bybit', {})
        ]
        
        for exchange_id, config in exchange_configs:
            try:
                exchange_class = getattr(ccxt_async, exchange_id)
                self.ccxt_exchanges[exchange_id] = exchange_class(config)
                self.logger.debug(f"Initialized CCXT exchange: {exchange_id}")
            except Exception as e:
                self.logger.warning(f"Failed to initialize {exchange_id}", error=str(e))
    
    async def fetch_market_data(
        self, 
        config: DataFetchConfig
    ) -> Tuple[pl.DataFrame, Dict[str, Any]]:
        """
        Fetch market data from multiple sources with fallback.
        
        Args:
            config: Data fetch configuration
            
        Returns:
            Tuple of (DataFrame with market data, metadata dict)
        """
        start_time = time.time()
        results = []
        metadata = {
            "sources_attempted": [],
            "sources_successful": [],
            "total_symbols": len(config.symbols),
            "fetch_time": 0,
            "errors": []
        }
        
        # Try each data source in order
        for source in config.sources:
            try:
                metadata["sources_attempted"].append(source.value)
                
                if source == DataSource.COINGECKO:
                    data = await self._fetch_coingecko_data(config)
                elif source == DataSource.CCXT and CCXT_AVAILABLE:
                    data = await self._fetch_ccxt_data(config)
                elif source == DataSource.YFINANCE and YFINANCE_AVAILABLE:
                    data = await self._fetch_yfinance_data(config)
                else:
                    continue
                
                if data:
                    results.extend(data)
                    metadata["sources_successful"].append(source.value)
                    
                    # If we have enough data, break early
                    if len(results) >= config.limit:
                        break
                        
            except Exception as e:
                error_msg = f"Failed to fetch from {source.value}: {str(e)}"
                self.logger.warning(error_msg)
                metadata["errors"].append(error_msg)
                
                if not config.fallback_enabled:
                    raise
        
        # Convert to DataFrame
        if results:
            df = self._create_dataframe(results)
            df = self._deduplicate_data(df)
            df = df.head(config.limit)  # Limit results
        else:
            df = self._create_empty_dataframe()
        
        metadata["fetch_time"] = time.time() - start_time
        metadata["symbols_found"] = df.height
        
        self.logger.info(
            "Market data fetch completed",
            sources_successful=metadata["sources_successful"],
            symbols_found=metadata["symbols_found"],
            fetch_time=metadata["fetch_time"]
        )
        
        return df, metadata
    
    async def _fetch_coingecko_data(self, config: DataFetchConfig) -> List[MarketDataPoint]:
        """Fetch data from CoinGecko."""
        try:
            # Use existing CoinGecko scraper
            market_data = await self.coingecko_scraper.get_market_data(
                vs_currency="usd",
                per_page=min(config.limit, 250),
                page=1
            )
            
            results = []
            for item in market_data:
                try:
                    # Filter by symbols if specified
                    if config.symbols and item.get("symbol", "").upper() not in [s.upper() for s in config.symbols]:
                        continue
                    
                    data_point = MarketDataPoint(
                        symbol=item.get("symbol", "").upper(),
                        price=float(item.get("current_price", 0)),
                        price_change_24h=float(item.get("price_change_24h", 0)),
                        price_change_percentage_24h=float(item.get("price_change_percentage_24h", 0)),
                        volume_24h=float(item.get("total_volume", 0)),
                        market_cap=item.get("market_cap"),
                        market_cap_rank=item.get("market_cap_rank"),
                        circulating_supply=item.get("circulating_supply"),
                        total_supply=item.get("total_supply"),
                        volume_to_market_cap_ratio=self._calculate_volume_ratio(
                            item.get("total_volume", 0),
                            item.get("market_cap", 1)
                        ),
                        timestamp=datetime.now(),
                        source=DataSource.COINGECKO
                    )
                    results.append(data_point)
                    
                except (ValueError, TypeError) as e:
                    self.logger.warning(f"Failed to parse CoinGecko item", error=str(e))
                    continue
            
            return results
            
        except Exception as e:
            self.logger.error("CoinGecko fetch failed", error=str(e))
            raise
    
    async def _fetch_ccxt_data(self, config: DataFetchConfig) -> List[MarketDataPoint]:
        """Fetch data from CCXT exchanges."""
        if not CCXT_AVAILABLE or not self.ccxt_exchanges:
            return []
        
        results = []
        
        # Try each exchange
        for exchange_id, exchange in self.ccxt_exchanges.items():
            try:
                # Load markets
                await exchange.load_markets()
                
                # Get tickers for specified symbols
                symbols_to_fetch = []
                if config.symbols:
                    for symbol in config.symbols:
                        # Try common trading pairs
                        for quote in ['USDT', 'USD', 'BTC', 'ETH']:
                            pair = f"{symbol.upper()}/{quote}"
                            if pair in exchange.markets:
                                symbols_to_fetch.append(pair)
                                break
                
                if not symbols_to_fetch:
                    continue
                
                # Fetch tickers
                tickers = await exchange.fetch_tickers(symbols_to_fetch[:20])  # Limit to avoid rate limits
                
                for symbol, ticker in tickers.items():
                    try:
                        base_symbol = symbol.split('/')[0]
                        
                        data_point = MarketDataPoint(
                            symbol=base_symbol,
                            price=float(ticker.get('last', 0)),
                            price_change_24h=float(ticker.get('change', 0)),
                            price_change_percentage_24h=float(ticker.get('percentage', 0)),
                            volume_24h=float(ticker.get('baseVolume', 0)),
                            market_cap=None,  # Not available from CCXT
                            market_cap_rank=None,
                            circulating_supply=None,
                            total_supply=None,
                            volume_to_market_cap_ratio=0.0,
                            timestamp=datetime.now(),
                            source=DataSource.CCXT
                        )
                        results.append(data_point)
                        
                    except (ValueError, TypeError) as e:
                        self.logger.warning(f"Failed to parse CCXT ticker", error=str(e))
                        continue
                
                # Close exchange connection
                await exchange.close()
                
            except Exception as e:
                self.logger.warning(f"CCXT exchange {exchange_id} failed", error=str(e))
                try:
                    await exchange.close()
                except:
                    pass
                continue
        
        return results
    
    async def _fetch_yfinance_data(self, config: DataFetchConfig) -> List[MarketDataPoint]:
        """Fetch data from Yahoo Finance."""
        if not YFINANCE_AVAILABLE:
            return []
        
        results = []
        
        try:
            # Convert crypto symbols to Yahoo Finance format
            yf_symbols = []
            for symbol in config.symbols:
                # Yahoo Finance crypto format: BTC-USD, ETH-USD, etc.
                yf_symbols.append(f"{symbol.upper()}-USD")
            
            if not yf_symbols:
                return results
            
            # Fetch data using yfinance
            tickers = yf.Tickers(' '.join(yf_symbols[:20]))  # Limit to avoid issues
            
            for yf_symbol in yf_symbols[:20]:
                try:
                    ticker = tickers.tickers[yf_symbol]
                    info = ticker.info
                    hist = ticker.history(period="2d")
                    
                    if hist.empty:
                        continue
                    
                    current_price = hist['Close'].iloc[-1]
                    prev_price = hist['Close'].iloc[-2] if len(hist) > 1 else current_price
                    price_change = current_price - prev_price
                    price_change_pct = (price_change / prev_price * 100) if prev_price != 0 else 0
                    
                    base_symbol = yf_symbol.replace('-USD', '')
                    
                    data_point = MarketDataPoint(
                        symbol=base_symbol,
                        price=float(current_price),
                        price_change_24h=float(price_change),
                        price_change_percentage_24h=float(price_change_pct),
                        volume_24h=float(hist['Volume'].iloc[-1]),
                        market_cap=info.get('marketCap'),
                        market_cap_rank=None,
                        circulating_supply=info.get('circulatingSupply'),
                        total_supply=info.get('totalSupply'),
                        volume_to_market_cap_ratio=self._calculate_volume_ratio(
                            hist['Volume'].iloc[-1],
                            info.get('marketCap', 1)
                        ),
                        timestamp=datetime.now(),
                        source=DataSource.YFINANCE
                    )
                    results.append(data_point)
                    
                except Exception as e:
                    self.logger.warning(f"Failed to fetch YFinance data for {yf_symbol}", error=str(e))
                    continue
            
        except Exception as e:
            self.logger.error("YFinance fetch failed", error=str(e))
            raise
        
        return results
    
    def _calculate_volume_ratio(self, volume: float, market_cap: Optional[float]) -> float:
        """Calculate volume to market cap ratio."""
        if not market_cap or market_cap == 0:
            return 0.0
        return volume / market_cap
    
    def _create_dataframe(self, data_points: List[MarketDataPoint]) -> pl.DataFrame:
        """Create Polars DataFrame from data points."""
        if not data_points:
            return self._create_empty_dataframe()
        
        # Convert to dictionary format
        data_dict = {
            "symbol": [dp.symbol for dp in data_points],
            "price": [dp.price for dp in data_points],
            "price_change_24h": [dp.price_change_24h for dp in data_points],
            "price_change_percentage_24h": [dp.price_change_percentage_24h for dp in data_points],
            "volume_24h": [dp.volume_24h for dp in data_points],
            "market_cap": [dp.market_cap for dp in data_points],
            "market_cap_rank": [dp.market_cap_rank for dp in data_points],
            "circulating_supply": [dp.circulating_supply for dp in data_points],
            "total_supply": [dp.total_supply for dp in data_points],
            "volume_to_market_cap_ratio": [dp.volume_to_market_cap_ratio for dp in data_points],
            "timestamp": [dp.timestamp for dp in data_points],
            "source": [dp.source.value for dp in data_points]
        }
        
        return pl.DataFrame(data_dict)
    
    def _create_empty_dataframe(self) -> pl.DataFrame:
        """Create empty DataFrame with correct schema."""
        return pl.DataFrame({
            "symbol": [],
            "price": [],
            "price_change_24h": [],
            "price_change_percentage_24h": [],
            "volume_24h": [],
            "market_cap": [],
            "market_cap_rank": [],
            "circulating_supply": [],
            "total_supply": [],
            "volume_to_market_cap_ratio": [],
            "timestamp": [],
            "source": []
        })
    
    def _deduplicate_data(self, df: pl.DataFrame) -> pl.DataFrame:
        """Remove duplicate entries, preferring CoinGecko data."""
        if df.height == 0:
            return df
        
        # Sort by source preference (CoinGecko first)
        source_priority = {"coingecko": 1, "ccxt": 2, "yfinance": 3}
        
        df = df.with_columns([
            pl.col("source").map_elements(
                lambda x: source_priority.get(x, 999),
                return_dtype=pl.Int32
            ).alias("source_priority")
        ])
        
        # Remove duplicates, keeping the one with highest priority (lowest number)
        df = df.sort(["symbol", "source_priority"]).unique(subset=["symbol"], keep="first")
        
        # Remove the temporary priority column
        df = df.drop("source_priority")
        
        return df
    
    async def get_top_cryptocurrencies(
        self, 
        limit: int = 50,
        sources: Optional[List[DataSource]] = None
    ) -> Tuple[pl.DataFrame, Dict[str, Any]]:
        """
        Get top cryptocurrencies by market cap.
        
        Args:
            limit: Number of cryptocurrencies to fetch
            sources: List of data sources to use (defaults to all available)
            
        Returns:
            Tuple of (DataFrame, metadata)
        """
        if sources is None:
            sources = [DataSource.COINGECKO]
            if CCXT_AVAILABLE:
                sources.append(DataSource.CCXT)
            if YFINANCE_AVAILABLE:
                sources.append(DataSource.YFINANCE)
        
        config = DataFetchConfig(
            sources=sources,
            symbols=[],  # Empty means get top coins
            limit=limit,
            fallback_enabled=True
        )
        
        return await self._fetch_with_enhanced_fallback(config)

    async def _fetch_with_enhanced_fallback(self, config: DataFetchConfig) -> Tuple[pl.DataFrame, Dict[str, Any]]:
        """Enhanced fallback strategy for robust data fetching."""
        start_time = time.time()

        # Primary: CoinGecko (most reliable, no auth required)
        try:
            cg_data = await self._fetch_coingecko_paginated(config.limit)
            if len(cg_data) >= max(1, config.limit * 0.8):  # 80% success threshold
                df = self._to_dataframe_robust(cg_data[:config.limit])
                metadata = self._create_success_metadata(cg_data, start_time, ["coingecko"])
                return df, metadata
        except Exception as e:
            self.logger.warning(f"CoinGecko primary fetch failed: {e}")

        # Fallback: Original fetch_market_data method
        try:
            return await self.fetch_market_data(config)
        except Exception as e:
            self.logger.error(f"All fallback methods failed: {e}")

            # Emergency fallback: return minimal data structure
            empty_df = self._create_empty_dataframe()
            metadata = {
                "fetch_time": time.time() - start_time,
                "sources_successful": [],
                "symbols_found": 0,
                "success_rate": 0.0,
                "error": str(e)
            }
            return empty_df, metadata

    async def _fetch_coingecko_paginated(self, limit: int) -> List[MarketDataPoint]:
        """Fetch CoinGecko data with pagination support."""
        results = []
        page = 1
        per_page = min(250, limit)  # CoinGecko max per page

        while len(results) < limit and page <= 5:  # Max 5 pages
            try:
                market_data = await self.coingecko_scraper.get_market_data(
                    vs_currency="usd",
                    per_page=per_page,
                    page=page,
                    order="market_cap_desc"
                )

                if not market_data:
                    break

                for item in market_data:
                    if len(results) >= limit:
                        break

                    try:
                        data_point = MarketDataPoint(
                            symbol=item.get("symbol", "").upper(),
                            price=float(item.get("current_price", 0)),
                            price_change_24h=float(item.get("price_change_24h", 0)),
                            price_change_percentage_24h=float(item.get("price_change_percentage_24h", 0)),
                            volume_24h=float(item.get("total_volume", 0)),
                            market_cap=item.get("market_cap"),
                            market_cap_rank=item.get("market_cap_rank"),
                            circulating_supply=item.get("circulating_supply"),
                            total_supply=item.get("total_supply"),
                            volume_to_market_cap_ratio=self._calculate_volume_ratio(
                                item.get("total_volume", 0),
                                item.get("market_cap", 1)
                            ),
                            timestamp=datetime.now(),
                            source=DataSource.COINGECKO
                        )
                        results.append(data_point)
                    except (ValueError, TypeError) as e:
                        self.logger.warning(f"Failed to parse CoinGecko item: {e}")
                        continue

                page += 1
                await asyncio.sleep(0.1)  # Rate limiting

            except Exception as e:
                self.logger.error(f"CoinGecko pagination failed on page {page}: {e}")
                break

        return results

    def _to_dataframe_robust(self, data: List[MarketDataPoint]) -> pl.DataFrame:
        """Convert data to DataFrame with robust schema validation."""
        if not data:
            return self._create_empty_dataframe()

        # Extract data with proper type handling
        rows = []
        for item in data:
            try:
                row = {
                    "symbol": str(item.symbol) if item.symbol else "",
                    "price": float(item.price) if item.price is not None else 0.0,
                    "volume_24h": float(item.volume_24h) if item.volume_24h is not None else 0.0,
                    "price_change_percentage_24h": float(item.price_change_percentage_24h) if item.price_change_percentage_24h is not None else 0.0,
                    "market_cap": int(item.market_cap) if item.market_cap is not None else 0,
                    "market_cap_rank": int(item.market_cap_rank) if item.market_cap_rank is not None else 0,
                    "circulating_supply": float(item.circulating_supply) if item.circulating_supply is not None else 0.0,
                    "total_supply": float(item.total_supply) if item.total_supply is not None else 0.0,
                    "volume_to_market_cap_ratio": float(item.volume_to_market_cap_ratio) if item.volume_to_market_cap_ratio is not None else 0.0,
                    "timestamp": item.timestamp,
                    "source": item.source.value
                }
                rows.append(row)
            except Exception as e:
                self.logger.warning(f"Failed to convert data point to row: {e}")
                continue

        if not rows:
            return self._create_empty_dataframe()

        try:
            return pl.DataFrame(rows)
        except Exception as e:
            self.logger.error(f"Failed to create DataFrame: {e}")
            return self._create_empty_dataframe()

    def _create_success_metadata(self, data: List[MarketDataPoint], start_time: float, sources: List[str]) -> Dict[str, Any]:
        """Create metadata for successful data fetch."""
        return {
            "fetch_time": time.time() - start_time,
            "sources_successful": sources,
            "symbols_found": len(data),
            "success_rate": 1.0,
            "data_quality_score": self._calculate_data_quality(data)
        }

    async def get_specific_cryptocurrencies(
        self,
        symbols: List[str],
        sources: Optional[List[DataSource]] = None
    ) -> Tuple[pl.DataFrame, Dict[str, Any]]:
        """
        Get data for specific cryptocurrency symbols.
        
        Args:
            symbols: List of cryptocurrency symbols
            sources: List of data sources to use
            
        Returns:
            Tuple of (DataFrame, metadata)
        """
        if sources is None:
            sources = [DataSource.COINGECKO]
            if CCXT_AVAILABLE:
                sources.append(DataSource.CCXT)
            if YFINANCE_AVAILABLE:
                sources.append(DataSource.YFINANCE)
        
        config = DataFetchConfig(
            sources=sources,
            symbols=symbols,
            limit=len(symbols),
            fallback_enabled=True
        )
        
        return await self.fetch_market_data(config)

    def _create_empty_dataframe(self) -> pl.DataFrame:
        """Create empty DataFrame with proper schema."""
        return pl.DataFrame({
            "symbol": pl.Series([], dtype=pl.Utf8),
            "price": pl.Series([], dtype=pl.Float64),
            "volume_24h": pl.Series([], dtype=pl.Float64),
            "price_change_percentage_24h": pl.Series([], dtype=pl.Float64),
            "market_cap": pl.Series([], dtype=pl.Int64),
            "market_cap_rank": pl.Series([], dtype=pl.Int64),
            "circulating_supply": pl.Series([], dtype=pl.Float64),
            "total_supply": pl.Series([], dtype=pl.Float64),
            "volume_to_market_cap_ratio": pl.Series([], dtype=pl.Float64),
            "timestamp": pl.Series([], dtype=pl.Datetime),
            "source": pl.Series([], dtype=pl.Utf8)
        })

    def _calculate_data_quality(self, data: List[MarketDataPoint]) -> float:
        """Calculate data quality score based on completeness."""
        if not data:
            return 0.0

        total_fields = 0
        complete_fields = 0

        for item in data:
            total_fields += 9  # Number of key fields
            if item.symbol: complete_fields += 1
            if item.price is not None and item.price > 0: complete_fields += 1
            if item.volume_24h is not None and item.volume_24h > 0: complete_fields += 1
            if item.price_change_percentage_24h is not None: complete_fields += 1
            if item.market_cap is not None and item.market_cap > 0: complete_fields += 1
            if item.market_cap_rank is not None and item.market_cap_rank > 0: complete_fields += 1
            if item.circulating_supply is not None: complete_fields += 1
            if item.total_supply is not None: complete_fields += 1
            if item.volume_to_market_cap_ratio is not None: complete_fields += 1

        return complete_fields / total_fields if total_fields > 0 else 0.0
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of all data sources."""
        health_status = {
            "overall_healthy": True,
            "sources": {}
        }
        
        # Check CoinGecko
        try:
            cg_healthy = await self.coingecko_scraper.health_check()
            health_status["sources"]["coingecko"] = {
                "healthy": cg_healthy,
                "available": True
            }
        except Exception as e:
            health_status["sources"]["coingecko"] = {
                "healthy": False,
                "available": True,
                "error": str(e)
            }
            health_status["overall_healthy"] = False
        
        # Check CCXT
        health_status["sources"]["ccxt"] = {
            "healthy": CCXT_AVAILABLE and len(self.ccxt_exchanges) > 0,
            "available": CCXT_AVAILABLE,
            "exchanges": list(self.ccxt_exchanges.keys()) if CCXT_AVAILABLE else []
        }
        
        # Check YFinance
        health_status["sources"]["yfinance"] = {
            "healthy": YFINANCE_AVAILABLE,
            "available": YFINANCE_AVAILABLE
        }
        
        return health_status
    
    async def close(self):
        """Close all connections."""
        try:
            await self.coingecko_scraper.close()
        except:
            pass
        
        for exchange in self.ccxt_exchanges.values():
            try:
                await exchange.close()
            except:
                pass
