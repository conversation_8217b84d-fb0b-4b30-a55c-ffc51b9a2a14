"""
Quantum feature encoding for cryptocurrency data.
Uses modern Qiskit 1.2+ patterns for efficient quantum state preparation.
"""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import structlog
from qiskit import QuantumCircuit
from qiskit.circuit import Parameter
from qiskit.circuit.library import RealAmplitudes, EfficientSU2
from qiskit.quantum_info import SparsePauliOp
import polars as pl

from ...core.logging import LoggerMixin

logger = structlog.get_logger(__name__)


class QuantumFeatureEncoder(LoggerMixin):
    """
    Quantum feature encoder for cryptocurrency market data.
    Encodes classical data into quantum states using modern Qiskit patterns.
    """
    
    def __init__(self, num_qubits: int = 4, encoding_type: str = "amplitude"):
        """
        Initialize quantum feature encoder.
        
        Args:
            num_qubits: Number of qubits for encoding
            encoding_type: Type of encoding ('amplitude', 'angle', 'basis')
        """
        self.num_qubits = num_qubits
        self.encoding_type = encoding_type
        self.max_features = 2**num_qubits if encoding_type == "amplitude" else num_qubits
        
        # Cache for compiled circuits
        self._circuit_cache: Dict[str, QuantumCircuit] = {}
        
        self.logger.info(
            "Quantum feature encoder initialized",
            num_qubits=num_qubits,
            encoding_type=encoding_type,
            max_features=self.max_features
        )
    
    def normalize_features(self, features: np.ndarray) -> np.ndarray:
        """
        Normalize features for quantum encoding.
        
        Args:
            features: Raw feature array
            
        Returns:
            Normalized features suitable for quantum encoding
        """
        try:
            # Handle different input shapes
            if features.ndim == 1:
                features = features.reshape(1, -1)
            
            # Limit to maximum encodable features
            if features.shape[1] > self.max_features:
                self.logger.warning(
                    "Truncating features to fit quantum encoding",
                    input_features=features.shape[1],
                    max_features=self.max_features
                )
                features = features[:, :self.max_features]
            
            # Pad if necessary
            elif features.shape[1] < self.max_features:
                padding = np.zeros((features.shape[0], self.max_features - features.shape[1]))
                features = np.hstack([features, padding])
            
            # Normalize based on encoding type
            if self.encoding_type == "amplitude":
                # For amplitude encoding, normalize to unit vector
                norms = np.linalg.norm(features, axis=1, keepdims=True)
                norms = np.where(norms == 0, 1, norms)  # Avoid division by zero
                normalized = features / norms
                
            elif self.encoding_type == "angle":
                # For angle encoding, normalize to [0, 2π]
                min_vals = np.min(features, axis=1, keepdims=True)
                max_vals = np.max(features, axis=1, keepdims=True)
                ranges = max_vals - min_vals
                ranges = np.where(ranges == 0, 1, ranges)  # Avoid division by zero
                normalized = 2 * np.pi * (features - min_vals) / ranges
                
            else:  # basis encoding
                # For basis encoding, use binary representation
                normalized = np.where(features > np.median(features, axis=1, keepdims=True), 1, 0)
            
            self.logger.debug(
                "Features normalized",
                input_shape=features.shape,
                output_shape=normalized.shape,
                encoding_type=self.encoding_type
            )
            
            return normalized
            
        except Exception as e:
            self.logger.error("Failed to normalize features", error=str(e))
            raise
    
    def create_amplitude_encoding_circuit(self, amplitudes: np.ndarray) -> QuantumCircuit:
        """
        Create quantum circuit for amplitude encoding.
        
        Args:
            amplitudes: Normalized amplitudes for encoding
            
        Returns:
            Quantum circuit with amplitude encoding
        """
        try:
            # Create circuit
            qc = QuantumCircuit(self.num_qubits)
            
            # Use angle encoding - map features to rotation angles
            # This creates meaningful quantum states that vary with input features
            for i in range(self.num_qubits):
                if i < len(amplitudes):
                    # Map amplitude to rotation angle (0 to π)
                    angle = amplitudes[i] * np.pi
                    qc.ry(angle, i)

            # Add some entanglement to make the encoding more expressive
            if self.num_qubits > 1:
                for i in range(self.num_qubits - 1):
                    qc.cx(i, i + 1)

            # Note: No measurements added - circuits for Estimator primitive
            
            self.logger.debug(
                "Amplitude encoding circuit created",
                num_qubits=self.num_qubits,
                num_amplitudes=len(amplitudes)
            )
            
            return qc
            
        except Exception as e:
            self.logger.error("Failed to create amplitude encoding circuit", error=str(e))
            raise
    
    def create_angle_encoding_circuit(self, angles: np.ndarray) -> QuantumCircuit:
        """
        Create quantum circuit for angle encoding.
        
        Args:
            angles: Angles for rotation gates
            
        Returns:
            Quantum circuit with angle encoding
        """
        try:
            qc = QuantumCircuit(self.num_qubits)
            
            # Apply rotation gates
            for i, angle in enumerate(angles[:self.num_qubits]):
                qc.ry(angle, i)
            
            # Add entangling gates for expressivity
            for i in range(self.num_qubits - 1):
                qc.cx(i, i + 1)
            
            # Note: No measurements added - circuits for Estimator primitive
            
            self.logger.debug(
                "Angle encoding circuit created",
                num_qubits=self.num_qubits,
                num_angles=len(angles)
            )
            
            return qc
            
        except Exception as e:
            self.logger.error("Failed to create angle encoding circuit", error=str(e))
            raise
    
    def create_basis_encoding_circuit(self, basis_states: np.ndarray) -> QuantumCircuit:
        """
        Create quantum circuit for basis encoding.
        
        Args:
            basis_states: Binary states for basis encoding
            
        Returns:
            Quantum circuit with basis encoding
        """
        try:
            qc = QuantumCircuit(self.num_qubits)
            
            # Apply X gates for |1⟩ states
            for i, state in enumerate(basis_states[:self.num_qubits]):
                if state == 1:
                    qc.x(i)
            
            # Note: No measurements added - circuits for Estimator primitive
            
            self.logger.debug(
                "Basis encoding circuit created",
                num_qubits=self.num_qubits,
                num_states=len(basis_states)
            )
            
            return qc
            
        except Exception as e:
            self.logger.error("Failed to create basis encoding circuit", error=str(e))
            raise
    
    def encode_features(self, features: np.ndarray) -> List[QuantumCircuit]:
        """
        Encode features into quantum circuits.
        
        Args:
            features: Feature array to encode
            
        Returns:
            List of quantum circuits with encoded features
        """
        try:
            # Normalize features
            normalized_features = self.normalize_features(features)
            
            circuits = []
            for feature_vector in normalized_features:
                # Create circuit based on encoding type
                if self.encoding_type == "amplitude":
                    circuit = self.create_amplitude_encoding_circuit(feature_vector)
                elif self.encoding_type == "angle":
                    circuit = self.create_angle_encoding_circuit(feature_vector)
                else:  # basis
                    circuit = self.create_basis_encoding_circuit(feature_vector)
                
                circuits.append(circuit)
            
            self.logger.info(
                "Features encoded to quantum circuits",
                num_circuits=len(circuits),
                encoding_type=self.encoding_type
            )
            
            return circuits
            
        except Exception as e:
            self.logger.error("Failed to encode features", error=str(e))
            raise
    
    def create_variational_circuit(self, num_layers: int = 2) -> QuantumCircuit:
        """
        Create variational quantum circuit for training.
        
        Args:
            num_layers: Number of variational layers
            
        Returns:
            Parameterized quantum circuit
        """
        try:
            # Use Qiskit's efficient ansatz
            ansatz = EfficientSU2(self.num_qubits, reps=num_layers)
            
            # Note: No measurements added - circuits for Estimator primitive
            
            self.logger.debug(
                "Variational circuit created",
                num_qubits=self.num_qubits,
                num_layers=num_layers,
                num_parameters=ansatz.num_parameters
            )
            
            return ansatz
            
        except Exception as e:
            self.logger.error("Failed to create variational circuit", error=str(e))
            raise
    
    def extract_market_features(self, market_data: pl.DataFrame) -> np.ndarray:
        """
        Extract relevant features from market data for quantum encoding.
        
        Args:
            market_data: Polars DataFrame with market data
            
        Returns:
            Feature array ready for quantum encoding
        """
        try:
            # Define available feature columns with fallbacks
            available_features = []

            # Price change percentage (primary signal)
            if "price_change_percentage_24h" in market_data.columns:
                values = market_data["price_change_percentage_24h"].fill_null(0).to_numpy()
                available_features.append(values)

            # Volume-based features
            if "volume_24h" in market_data.columns and "market_cap" in market_data.columns:
                volume = market_data["volume_24h"].fill_null(1).to_numpy()
                market_cap = market_data["market_cap"].fill_null(1).to_numpy()
                volume_ratio = np.log1p(volume / np.maximum(market_cap, 1))
                available_features.append(volume_ratio)
            elif "volume_24h" in market_data.columns:
                volume = market_data["volume_24h"].fill_null(1).to_numpy()
                available_features.append(np.log1p(volume))

            # Market cap rank (inverted so smaller rank = higher value)
            if "market_cap_rank" in market_data.columns:
                ranks = market_data["market_cap_rank"].fill_null(1000).to_numpy()
                # Invert and normalize: rank 1 -> high value, rank 1000 -> low value
                rank_feature = 1.0 / np.maximum(ranks, 1)
                available_features.append(rank_feature)

            # Price-based features
            if "price" in market_data.columns:
                prices = market_data["price"].fill_null(1).to_numpy()
                price_feature = np.log1p(prices)
                available_features.append(price_feature)

            # Ensure we have exactly the right number of features for quantum encoding
            target_features = 2 ** self.num_qubits  # 4 features for 2 qubits

            if len(available_features) < target_features:
                # Pad with derived features if we don't have enough
                while len(available_features) < target_features:
                    if len(available_features) > 0:
                        # Create derived feature from existing ones
                        base_feature = available_features[len(available_features) % len(available_features)]
                        derived_feature = np.sin(base_feature) if len(available_features) % 2 == 0 else np.cos(base_feature)
                        available_features.append(derived_feature)
                    else:
                        # Fallback to random-like features based on row index
                        row_indices = np.arange(len(market_data))
                        available_features.append(np.sin(row_indices * 0.1))

            # Take exactly the number we need
            features = available_features[:target_features]
            
            # Stack features
            feature_array = np.column_stack(features)
            
            # Add derived features
            if len(features) >= 2:
                # Price momentum indicator
                momentum = feature_array[:, 0] * feature_array[:, 1]
                feature_array = np.column_stack([feature_array, momentum])
            
            self.logger.info(
                "Market features extracted",
                num_samples=feature_array.shape[0],
                num_features=feature_array.shape[1]
            )
            
            return feature_array
            
        except Exception as e:
            self.logger.error("Failed to extract market features", error=str(e))
            raise
    
    def get_encoding_stats(self) -> Dict[str, Any]:
        """Get encoding statistics and configuration."""
        return {
            "num_qubits": self.num_qubits,
            "encoding_type": self.encoding_type,
            "max_features": self.max_features,
            "cached_circuits": len(self._circuit_cache),
            "supported_encodings": ["amplitude", "angle", "basis"]
        }