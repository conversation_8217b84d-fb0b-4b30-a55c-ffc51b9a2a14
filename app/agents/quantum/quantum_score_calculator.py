"""
Modern quantum score calculator for cryptocurrency analysis.
Uses Qiskit 1.2+ with advanced quantum algorithms and machine learning.
"""

import time
import pickle
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
import numpy as np
import structlog
import polars as pl
from qiskit import QuantumCircuit
from qiskit_aer import AerSimulator
from qiskit_aer.primitives import EstimatorV2 as Estimator
from qiskit.quantum_info import SparsePauliOp
from qiskit.circuit.library import RealAmplitudes
from qiskit.transpiler import generate_preset_pass_manager
from scipy.optimize import minimize
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

from .quantum_feature_encoder import QuantumFeatureEncoder
from .quantum_circuit_optimizer import QuantumCircuitOptimizer
from ...core.logging import LoggerMixin
from ...core.config import settings

logger = structlog.get_logger(__name__)


class QuantumScoreCalculator(LoggerMixin):
    """
    Advanced quantum score calculator for cryptocurrency analysis.
    Combines quantum feature encoding, variational algorithms, and classical ML.
    """
    
    def __init__(
        self,
        num_qubits: int = 4,
        num_layers: int = 2,
        encoding_type: str = "amplitude",
        optimizer_type: str = "SPSA"
    ):
        """
        Initialize quantum score calculator.
        
        Args:
            num_qubits: Number of qubits for quantum circuits
            num_layers: Number of variational layers
            encoding_type: Feature encoding method
            optimizer_type: Classical optimizer for VQE
        """
        self.num_qubits = num_qubits
        self.num_layers = num_layers
        self.encoding_type = encoding_type
        self.optimizer_type = optimizer_type
        
        # Initialize components
        self.feature_encoder = QuantumFeatureEncoder(num_qubits, encoding_type)
        self.circuit_optimizer = QuantumCircuitOptimizer()
        
        # Quantum backend
        self.backend = AerSimulator()
        self.estimator = Estimator()
        
        # Classical components
        self.scaler = StandardScaler()
        self.is_trained = False
        self.model_parameters: Optional[np.ndarray] = None
        self.training_history: List[Dict[str, Any]] = []
        
        # Performance tracking
        self.calculation_stats = {
            "scores_calculated": 0,
            "total_calculation_time": 0.0,
            "average_calculation_time": 0.0,
            "quantum_circuit_executions": 0
        }
        
        # Model persistence
        self.model_path = Path("data/quantum_models")
        self.model_path.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(
            "Quantum score calculator initialized",
            num_qubits=num_qubits,
            num_layers=num_layers,
            encoding_type=encoding_type,
            optimizer_type=optimizer_type
        )
    
    def _get_optimizer_method(self) -> str:
        """Get scipy optimizer method name."""
        if self.optimizer_type == "SPSA":
            return "BFGS"  # Use BFGS as alternative to SPSA
        elif self.optimizer_type == "COBYLA":
            return "COBYLA"
        else:
            self.logger.warning(f"Unknown optimizer {self.optimizer_type}, using BFGS")
            return "BFGS"
    
    def create_quantum_model(self) -> Tuple[QuantumCircuit, SparsePauliOp]:
        """
        Create quantum variational model for scoring.
        
        Returns:
            Tuple of (ansatz_circuit, observable)
        """
        try:
            # Create variational ansatz using RealAmplitudes
            ansatz = RealAmplitudes(
                self.num_qubits,
                reps=self.num_layers,
                insert_barriers=True
            )
            
            # Create observable for expectation value
            # Use Z measurements on all qubits
            pauli_strings = []
            for i in range(self.num_qubits):
                pauli_string = ['I'] * self.num_qubits
                pauli_string[i] = 'Z'
                pauli_strings.append(''.join(pauli_string))
            
            # Combine Pauli strings with equal weights
            coeffs = [1.0 / self.num_qubits] * len(pauli_strings)
            observable = SparsePauliOp(pauli_strings, coeffs)
            
            self.logger.debug(
                "Quantum model created",
                num_parameters=ansatz.num_parameters,
                num_pauli_strings=len(pauli_strings)
            )
            
            return ansatz, observable
            
        except Exception as e:
            self.logger.error("Failed to create quantum model", error=str(e))
            raise
    
    def prepare_training_data(
        self, 
        market_data: pl.DataFrame,
        target_column: str = "price_change_percentage_24h"
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare training data from market data.
        
        Args:
            market_data: Market data DataFrame
            target_column: Target variable for training
            
        Returns:
            Tuple of (features, targets)
        """
        try:
            # Extract features using quantum encoder
            features = self.feature_encoder.extract_market_features(market_data)
            
            # Extract targets
            if target_column in market_data.columns:
                targets = market_data[target_column].fill_null(0).to_numpy()
            else:
                self.logger.warning(f"Target column {target_column} not found, using zeros")
                targets = np.zeros(len(market_data))
            
            # Convert targets to binary classification (positive/negative returns)
            binary_targets = (targets > 0).astype(int)
            
            # Scale features
            features_scaled = self.scaler.fit_transform(features)
            
            self.logger.info(
                "Training data prepared",
                num_samples=len(features_scaled),
                num_features=features_scaled.shape[1],
                positive_samples=np.sum(binary_targets),
                negative_samples=len(binary_targets) - np.sum(binary_targets)
            )
            
            return features_scaled, binary_targets
            
        except Exception as e:
            self.logger.error("Failed to prepare training data", error=str(e))
            raise
    
    def quantum_cost_function(
        self, 
        parameters: np.ndarray,
        ansatz: QuantumCircuit,
        observable: SparsePauliOp,
        features: np.ndarray,
        targets: np.ndarray
    ) -> float:
        """
        Quantum cost function for variational training.
        
        Args:
            parameters: Variational parameters
            ansatz: Quantum ansatz circuit
            observable: Observable for measurement
            features: Training features
            targets: Training targets
            
        Returns:
            Cost value
        """
        try:
            total_cost = 0.0
            num_samples = min(len(features), 50)  # Limit for computational efficiency
            
            for i in range(num_samples):
                # Encode features into quantum circuit
                feature_circuits = self.feature_encoder.encode_features(features[i:i+1])
                feature_circuit = feature_circuits[0]
                
                # Combine with ansatz and decompose
                combined_circuit = feature_circuit.compose(ansatz)
                combined_circuit = combined_circuit.decompose()  # Decompose RealAmplitudes

                # Assign parameters with error handling
                try:
                    if len(ansatz.parameters) > 0:
                        param_dict = dict(zip(ansatz.parameters, parameters))
                        bound_circuit = combined_circuit.assign_parameters(param_dict)
                    else:
                        bound_circuit = combined_circuit

                    # Calculate expectation value using EstimatorV2 API
                    # EstimatorV2 handles transpilation internally
                    job = self.estimator.run([(bound_circuit, observable)])
                    result = job.result()

                    # Handle different result formats robustly
                    evs = result[0].data.evs
                    try:
                        if hasattr(evs, '__len__') and len(evs) > 0:
                            expectation_value = float(evs[0])
                        elif hasattr(evs, 'item'):  # numpy scalar
                            expectation_value = evs.item()
                        else:
                            expectation_value = float(evs)
                    except (TypeError, IndexError):
                        # Fallback for any unexpected format
                        expectation_value = float(evs) if np.isscalar(evs) else 0.0
                except Exception as circuit_error:
                    self.logger.warning(f"Circuit execution failed for sample {i}: {circuit_error}")
                    # Use a neutral expectation value for failed circuits
                    expectation_value = 0.0
                
                # Convert to prediction (sigmoid-like)
                prediction = (expectation_value + 1) / 2  # Map [-1,1] to [0,1]
                
                # Binary cross-entropy loss
                target = targets[i]
                epsilon = 1e-15  # Prevent log(0)
                prediction = np.clip(prediction, epsilon, 1 - epsilon)
                
                cost = -(target * np.log(prediction) + (1 - target) * np.log(1 - prediction))
                total_cost += cost
            
            average_cost = total_cost / num_samples
            
            self.calculation_stats["quantum_circuit_executions"] += num_samples
            
            return average_cost
            
        except Exception as e:
            self.logger.error("Error in quantum cost function", error=str(e))
            # Return high cost on error to indicate poor performance
            return 1.0
    
    def train_quantum_model(
        self, 
        market_data: pl.DataFrame,
        max_iterations: int = 50,
        target_column: str = "price_change_percentage_24h"
    ) -> Dict[str, Any]:
        """
        Train the quantum variational model.
        
        Args:
            market_data: Training data
            max_iterations: Maximum optimization iterations
            target_column: Target variable
            
        Returns:
            Training results and metrics
        """
        try:
            start_time = time.time()
            
            self.logger.info("Starting quantum model training")
            
            # Prepare data
            features, targets = self.prepare_training_data(market_data, target_column)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                features, targets, test_size=0.2, random_state=42
            )
            
            # Create quantum model
            ansatz, observable = self.create_quantum_model()
            
            # Initialize parameters
            initial_params = np.random.uniform(0, 2*np.pi, ansatz.num_parameters)
            
            # Set up optimizer
            optimizer_method = self._get_optimizer_method()

            # Training loop with callback
            iteration_costs = []
            iteration_count = [0]  # Use list to allow modification in callback

            def callback(params):
                cost = self.quantum_cost_function(params, ansatz, observable, X_train, y_train)
                iteration_costs.append(cost)
                iteration_count[0] += 1
                if iteration_count[0] % 10 == 0:
                    self.logger.debug(
                        "Training progress",
                        iteration=iteration_count[0],
                        cost=cost
                    )

            # Run optimization
            self.logger.info("Running quantum optimization")

            result = minimize(
                fun=lambda params: self.quantum_cost_function(
                    params, ansatz, observable, X_train, y_train
                ),
                x0=initial_params,
                method=optimizer_method,
                callback=callback,
                options={'maxiter': max_iterations}
            )
            
            # Store trained parameters
            self.model_parameters = result.x
            self.is_trained = True

            # Evaluate on test set
            if self.model_parameters is not None:
                test_cost = self.quantum_cost_function(
                    self.model_parameters, ansatz, observable, X_test, y_test
                )
            else:
                test_cost = 1.0
            
            training_time = time.time() - start_time
            
            # Store training history
            training_record = {
                "timestamp": time.time(),
                "training_samples": len(X_train),
                "test_samples": len(X_test),
                "final_cost": result.fun,
                "test_cost": test_cost,
                "iterations": len(iteration_costs),
                "training_time": training_time,
                "parameters": self.model_parameters.tolist() if self.model_parameters is not None else [],
                "cost_history": iteration_costs
            }
            
            self.training_history.append(training_record)
            
            self.logger.info(
                "Quantum model training completed",
                final_cost=result.fun,
                test_cost=test_cost,
                iterations=len(iteration_costs),
                training_time=f"{training_time:.2f}s"
            )
            
            return training_record
            
        except Exception as e:
            self.logger.error("Failed to train quantum model", error=str(e))
            raise
    
    def calculate_quantum_score(
        self, 
        market_data: Union[pl.DataFrame, np.ndarray],
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Calculate quantum score for market data.
        
        Args:
            market_data: Market data to score
            use_cache: Whether to use cached results
            
        Returns:
            Quantum scores and metrics
        """
        try:
            start_time = time.time()
            
            if not self.is_trained:
                self.logger.warning("Model not trained, using classical fallback scoring")
                return self._generate_classical_fallback_scores(market_data)
            
            # Prepare features using same logic as training
            if isinstance(market_data, pl.DataFrame):
                features = self._extract_training_features(market_data)
                symbols = market_data.get_column("symbol").to_list() if "symbol" in market_data.columns else None
            else:
                features = market_data
                symbols = None
            
            # Scale features
            features_scaled = self.scaler.transform(features)
            
            # Create quantum model
            ansatz, observable = self.create_quantum_model()
            
            # Calculate scores
            scores = []
            confidence_scores = []
            
            for i, feature_vector in enumerate(features_scaled):
                # Encode features
                feature_circuits = self.feature_encoder.encode_features(feature_vector.reshape(1, -1))
                feature_circuit = feature_circuits[0]

                # Combine with trained ansatz
                try:
                    combined_circuit = feature_circuit.compose(ansatz)
                    if self.model_parameters is not None and len(ansatz.parameters) > 0:
                        param_dict = dict(zip(ansatz.parameters, self.model_parameters))
                        bound_circuit = combined_circuit.assign_parameters(param_dict)
                    else:
                        bound_circuit = combined_circuit

                    # Get expectation value using correct Qiskit API
                    job = self.estimator.run([bound_circuit], [observable])
                    result = job.result()
                    expectation_value = result.values[0]
                except Exception as circuit_error:
                    self.logger.warning(f"Circuit execution failed for sample {i}: {circuit_error}")
                    # Use neutral expectation value for failed circuits
                    expectation_value = 0.0
                
                # Convert to score [0, 1]
                quantum_score = (expectation_value + 1) / 2
                
                # Calculate confidence based on expectation value magnitude
                confidence = abs(expectation_value)
                
                scores.append(quantum_score)
                confidence_scores.append(confidence)
            
            calculation_time = time.time() - start_time
            
            # Update statistics
            self.calculation_stats["scores_calculated"] += len(scores)
            self.calculation_stats["total_calculation_time"] += calculation_time
            self.calculation_stats["average_calculation_time"] = (
                self.calculation_stats["total_calculation_time"] / 
                self.calculation_stats["scores_calculated"]
            )
            
            # Prepare results
            results = {
                "quantum_scores": scores,
                "confidence_scores": confidence_scores,
                "average_score": np.mean(scores),
                "score_std": np.std(scores),
                "high_confidence_count": sum(1 for c in confidence_scores if c > 0.5),
                "calculation_time": calculation_time,
                "num_samples": len(scores),
                "model_trained": self.is_trained
            }
            
            if symbols:
                results["symbols"] = symbols
                # Create symbol-score mapping
                results["symbol_scores"] = dict(zip(symbols, scores))
            
            self.logger.info(
                "Quantum scores calculated",
                num_samples=len(scores),
                average_score=f"{np.mean(scores):.3f}",
                calculation_time=f"{calculation_time:.3f}s"
            )
            
            return results
            
        except Exception as e:
            self.logger.error("Failed to calculate quantum scores", error=str(e))
            raise
    
    def _generate_classical_fallback_scores(self, market_data: Union[pl.DataFrame, np.ndarray]) -> Dict[str, Any]:
        """Generate deterministic scores based on classical features when quantum model is not trained."""
        if isinstance(market_data, pl.DataFrame):
            num_samples = len(market_data)
            symbols = market_data.get_column("symbol").to_list() if "symbol" in market_data.columns else None

            # Extract classical features for scoring
            features = self.feature_encoder.extract_market_features(market_data)
        else:
            num_samples = len(market_data)
            symbols = None
            features = market_data

        # Classical scoring based on normalized features
        scores = []
        confidence_scores = []

        for i in range(num_samples):
            if isinstance(market_data, pl.DataFrame):
                # Use actual market data for classical scoring
                price_change = market_data.row(i, named=True).get("price_change_percentage_24h", 0)
                volume_ratio = market_data.row(i, named=True).get("volume_to_market_cap_ratio", 0.05)
                market_cap_rank = market_data.row(i, named=True).get("market_cap_rank", 100)

                # Classical scoring formula based on market indicators
                # Higher score for positive price change, good volume, lower rank
                price_score = max(0, min(1, (price_change + 10) / 20))  # Normalize -10% to +10% range
                volume_score = min(1, volume_ratio * 10)  # Higher volume ratio = higher score
                rank_score = max(0, (200 - market_cap_rank) / 200)  # Lower rank = higher score

                # Weighted combination
                classical_score = (0.4 * price_score + 0.3 * volume_score + 0.3 * rank_score)
                confidence = 0.6  # Medium confidence for classical scoring
            else:
                # Use feature vector for scoring
                feature_vector = features[i] if len(features.shape) > 1 else features
                # Normalize features to [0, 1] range and take mean
                normalized_features = (feature_vector - feature_vector.min()) / (feature_vector.max() - feature_vector.min() + 1e-8)
                classical_score = np.mean(normalized_features)
                confidence = 0.5

            scores.append(float(classical_score))
            confidence_scores.append(float(confidence))

        results = {
            "quantum_scores": scores,
            "confidence_scores": confidence_scores,
            "average_score": np.mean(scores),
            "score_std": np.std(scores),
            "high_confidence_count": sum(1 for c in confidence_scores if c > 0.5),
            "calculation_time": 0.001,
            "num_samples": num_samples,
            "model_trained": False,
            "warning": "Model not trained - using classical fallback scoring based on market indicators"
        }

        if symbols:
            results["symbols"] = symbols
            results["symbol_scores"] = dict(zip(symbols, scores))

        return results

    def check_quantum_system_health(self) -> Dict[str, Any]:
        """Check the health of the quantum computing system."""
        health_status = {
            "overall_status": "healthy",
            "issues": [],
            "warnings": [],
            "system_info": {},
            "performance_metrics": {}
        }

        try:
            # Test basic quantum circuit creation
            test_circuit = QuantumCircuit(2)
            test_circuit.h(0)
            test_circuit.cx(0, 1)

            # Test feature encoder
            try:
                encoder_stats = self.feature_encoder.get_encoding_stats()
                health_status["system_info"]["feature_encoder"] = encoder_stats
            except Exception as e:
                health_status["issues"].append(f"Feature encoder error: {e}")
                health_status["overall_status"] = "degraded"

            # Test circuit optimizer
            try:
                optimizer_stats = self.circuit_optimizer.get_optimization_statistics()
                health_status["system_info"]["circuit_optimizer"] = optimizer_stats
            except Exception as e:
                health_status["issues"].append(f"Circuit optimizer error: {e}")
                health_status["overall_status"] = "degraded"

            # Test quantum backend
            try:
                # Simple test circuit execution
                job = self.estimator.run([(test_circuit, SparsePauliOp.from_list([("ZZ", 1.0)]))])
                result = job.result()
                health_status["system_info"]["backend_test"] = "passed"
                health_status["performance_metrics"]["test_execution_time"] = 0.1  # Placeholder
            except Exception as e:
                health_status["issues"].append(f"Quantum backend error: {e}")
                health_status["overall_status"] = "unhealthy"

            # Check model training status
            if not self.is_trained:
                health_status["warnings"].append("Quantum model is not trained - using classical fallback")

            # Check calculation statistics
            health_status["performance_metrics"].update(self.calculation_stats)

            # Determine overall status
            if len(health_status["issues"]) > 2:
                health_status["overall_status"] = "unhealthy"
            elif len(health_status["issues"]) > 0:
                health_status["overall_status"] = "degraded"

        except Exception as e:
            health_status["overall_status"] = "unhealthy"
            health_status["issues"].append(f"System health check failed: {e}")

        return health_status

    def _extract_training_features(self, data: pl.DataFrame) -> np.ndarray:
        """Extract features using same logic as training pipeline."""

        # Use same feature columns as training pipeline
        available_feature_columns = [
            "price_change_percentage_24h",
            "volume_to_market_cap_ratio",
            "market_cap_rank",
            "volume_24h"
        ]

        feature_data = []
        for col in available_feature_columns:
            if col in data.columns:
                values = data[col].to_numpy()
                # Handle nulls
                values = np.nan_to_num(values, nan=0.0)

                # Normalize features to [0, 1] range
                if col == "price_change_percentage_24h":
                    # Map price changes from [-100, 100] to [0, 1]
                    values = (values + 100) / 200
                elif col == "volume_to_market_cap_ratio":
                    # Cap at 1.0 and normalize
                    values = np.minimum(values, 1.0)
                elif col == "market_cap_rank":
                    # Invert rank (lower rank = higher value) and normalize
                    values = np.maximum(1000 - values, 0) / 1000
                elif col == "volume_24h":
                    # Log normalize large values
                    values = np.log1p(values)
                    values = (values - np.min(values)) / (np.max(values) - np.min(values) + 1e-8)

                feature_data.append(values)
            else:
                feature_data.append(np.zeros(data.height))

        # Ensure we have exactly 4 features for 4 qubits
        while len(feature_data) < self.num_qubits:
            feature_data.append(np.zeros(data.height))

        features = np.column_stack(feature_data[:self.num_qubits])
        return features

    def save_model(self, filename: Optional[str] = None) -> str:
        """Save trained model to disk."""
        try:
            if not self.is_trained:
                raise ValueError("No trained model to save")
            
            if filename is None:
                timestamp = int(time.time())
                filename = f"quantum_model_{timestamp}.pkl"
            
            filepath = self.model_path / filename
            
            model_data = {
                "parameters": self.model_parameters,
                "scaler": self.scaler,
                "config": {
                    "num_qubits": self.num_qubits,
                    "num_layers": self.num_layers,
                    "encoding_type": self.encoding_type,
                    "optimizer_type": self.optimizer_type
                },
                "training_history": self.training_history,
                "stats": self.calculation_stats
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info("Model saved", filepath=str(filepath))
            return str(filepath)
            
        except Exception as e:
            self.logger.error("Failed to save model", error=str(e))
            raise
    
    def load_model(self, filepath: str) -> bool:
        """Load trained model from disk."""
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model_parameters = model_data["parameters"]
            self.scaler = model_data["scaler"]
            self.training_history = model_data.get("training_history", [])
            self.calculation_stats = model_data.get("stats", self.calculation_stats)
            
            # Verify config compatibility
            config = model_data["config"]
            if (config["num_qubits"] != self.num_qubits or 
                config["num_layers"] != self.num_layers):
                self.logger.warning(
                    "Model config mismatch",
                    saved_config=config,
                    current_config={
                        "num_qubits": self.num_qubits,
                        "num_layers": self.num_layers,
                        "encoding_type": self.encoding_type
                    }
                )
            
            self.is_trained = True
            
            self.logger.info("Model loaded successfully", filepath=filepath)
            return True
            
        except Exception as e:
            self.logger.error("Failed to load model", error=str(e), filepath=filepath)
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive model information."""
        return {
            "is_trained": self.is_trained,
            "num_qubits": self.num_qubits,
            "num_layers": self.num_layers,
            "encoding_type": self.encoding_type,
            "optimizer_type": self.optimizer_type,
            "num_parameters": len(self.model_parameters) if self.model_parameters is not None else 0,
            "training_sessions": len(self.training_history),
            "calculation_stats": self.calculation_stats,
            "feature_encoder_stats": self.feature_encoder.get_encoding_stats(),
            "circuit_optimizer_stats": self.circuit_optimizer.get_optimization_statistics()
        }