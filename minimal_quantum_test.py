#!/usr/bin/env python3
"""
Minimal quantum test to verify EstimatorV2 is working
"""

import numpy as np
from qiskit import QuantumCircuit
from qiskit.quantum_info import SparsePauliOp
from qiskit_aer.primitives import EstimatorV2 as Estimator

def test_basic_estimator():
    """Test basic EstimatorV2 functionality"""
    print("🔍 TESTING BASIC ESTIMATORV2 FUNCTIONALITY")
    print("=" * 60)
    
    # Create simple circuit
    qc = QuantumCircuit(1)
    # Start in |0⟩ state, Z measurement should give +1
    
    # Create Z observable
    observable = SparsePauliOp.from_list([("Z", 1.0)])
    
    # Create estimator
    estimator = Estimator()
    
    print("📊 Testing |0⟩ state with Z measurement...")
    job = estimator.run([(qc, observable)])
    result = job.result()
    
    evs = result[0].data.evs
    print(f"Raw result: {evs}")
    print(f"Type: {type(evs)}")
    print(f"Shape: {evs.shape if hasattr(evs, 'shape') else 'No shape'}")
    
    # Extract value
    if hasattr(evs, 'item'):
        exp_val = evs.item()
    else:
        exp_val = float(evs)
    
    print(f"Expectation value: {exp_val}")
    
    # Should be close to +1
    if abs(exp_val - 1.0) < 0.1:
        print("✅ PASSED: |0⟩ state gives Z expectation ≈ +1")
    else:
        print(f"❌ FAILED: Expected ≈1.0, got {exp_val}")
        return False
    
    # Test |1⟩ state
    print("\n📊 Testing |1⟩ state with Z measurement...")
    qc_1 = QuantumCircuit(1)
    qc_1.x(0)  # Flip to |1⟩
    
    job = estimator.run([(qc_1, observable)])
    result = job.result()
    
    evs = result[0].data.evs
    if hasattr(evs, 'item'):
        exp_val = evs.item()
    else:
        exp_val = float(evs)
    
    print(f"Expectation value: {exp_val}")
    
    # Should be close to -1
    if abs(exp_val - (-1.0)) < 0.1:
        print("✅ PASSED: |1⟩ state gives Z expectation ≈ -1")
    else:
        print(f"❌ FAILED: Expected ≈-1.0, got {exp_val}")
        return False
    
    # Test superposition state
    print("\n📊 Testing |+⟩ state with Z measurement...")
    qc_plus = QuantumCircuit(1)
    qc_plus.h(0)  # Create |+⟩ = (|0⟩ + |1⟩)/√2
    
    job = estimator.run([(qc_plus, observable)])
    result = job.result()
    
    evs = result[0].data.evs
    if hasattr(evs, 'item'):
        exp_val = evs.item()
    else:
        exp_val = float(evs)
    
    print(f"Expectation value: {exp_val}")
    
    # Should be close to 0
    if abs(exp_val) < 0.1:
        print("✅ PASSED: |+⟩ state gives Z expectation ≈ 0")
    else:
        print(f"❌ FAILED: Expected ≈0.0, got {exp_val}")
        return False
    
    # Test X measurement on |+⟩ state
    print("\n📊 Testing |+⟩ state with X measurement...")
    x_observable = SparsePauliOp.from_list([("X", 1.0)])
    
    job = estimator.run([(qc_plus, x_observable)])
    result = job.result()
    
    evs = result[0].data.evs
    if hasattr(evs, 'item'):
        exp_val = evs.item()
    else:
        exp_val = float(evs)
    
    print(f"Expectation value: {exp_val}")
    
    # Should be close to +1
    if abs(exp_val - 1.0) < 0.1:
        print("✅ PASSED: |+⟩ state gives X expectation ≈ +1")
        return True
    else:
        print(f"❌ FAILED: Expected ≈1.0, got {exp_val}")
        return False

def test_parameterized_circuit():
    """Test parameterized circuit"""
    print("\n🔍 TESTING PARAMETERIZED CIRCUIT")
    print("=" * 60)
    
    from qiskit.circuit import Parameter
    
    # Create parameterized circuit
    theta = Parameter('θ')
    qc = QuantumCircuit(1)
    qc.ry(theta, 0)
    
    # Z observable
    observable = SparsePauliOp.from_list([("Z", 1.0)])
    
    estimator = Estimator()
    
    # Test different parameter values
    test_angles = [0, np.pi/4, np.pi/2, 3*np.pi/4, np.pi]
    expectation_values = []
    
    print("📊 Testing parameter sensitivity...")
    for angle in test_angles:
        bound_circuit = qc.assign_parameters({theta: angle})
        
        job = estimator.run([(bound_circuit, observable)])
        result = job.result()
        
        evs = result[0].data.evs
        if hasattr(evs, 'item'):
            exp_val = evs.item()
        else:
            exp_val = float(evs)
        
        expectation_values.append(exp_val)
        print(f"  θ = {angle:.3f}: Z expectation = {exp_val:.6f}")
    
    # Check variance
    variance = np.var(expectation_values)
    print(f"\n📊 Expectation value variance: {variance:.6f}")
    
    if variance > 0.1:
        print("✅ PASSED: Parameterized circuit shows sensitivity")
        return True
    else:
        print("❌ FAILED: Parameterized circuit shows no sensitivity")
        return False

def main():
    """Run minimal quantum tests"""
    print("🚀 MINIMAL QUANTUM ESTIMATORV2 TEST")
    print("=" * 80)
    
    results = []
    
    # Test 1: Basic EstimatorV2 functionality
    results.append(test_basic_estimator())
    
    # Test 2: Parameterized circuit
    results.append(test_parameterized_circuit())
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 MINIMAL TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    test_names = ["Basic EstimatorV2", "Parameterized Circuit"]
    for name, result in zip(test_names, results):
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ESTIMATORV2 WORKING CORRECTLY")
        print("✅ Quantum circuit execution is functional")
    else:
        print("⚠️  ESTIMATORV2 ISSUES DETECTED")
        print("❌ Fundamental quantum execution problems")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
