from ccxt.base.types import Entry


class ImplicitAPI:
    v2_get_platform_status = v2GetPlatformStatus = Entry('platform/status', 'v2', 'GET', {'cost': 3})
    v2_get_tickers = v2GetTickers = Entry('tickers', 'v2', 'GET', {'cost': 1})
    v2_get_ticker_symbol = v2GetTickerSymbol = Entry('ticker/{symbol}', 'v2', 'GET', {'cost': 1})
    v2_get_tickers_hist = v2GetTickersHist = Entry('tickers/hist', 'v2', 'GET', {'cost': 1})
    v2_get_trades_symbol_hist = v2GetTradesSymbolHist = Entry('trades/{symbol}/hist', 'v2', 'GET', {'cost': 1})
    v2_get_book_symbol_precision = v2GetBookSymbolPrecision = Entry('book/{symbol}/{precision}', 'v2', 'GET', {'cost': 0.375})
    v2_get_book_symbol_p0 = v2GetBookSymbolP0 = Entry('book/{symbol}/P0', 'v2', 'GET', {'cost': 0.375})
    v2_get_book_symbol_p1 = v2GetBookSymbolP1 = Entry('book/{symbol}/P1', 'v2', 'GET', {'cost': 0.375})
    v2_get_book_symbol_p2 = v2GetBookSymbolP2 = Entry('book/{symbol}/P2', 'v2', 'GET', {'cost': 0.375})
    v2_get_book_symbol_p3 = v2GetBookSymbolP3 = Entry('book/{symbol}/P3', 'v2', 'GET', {'cost': 0.375})
    v2_get_book_symbol_r0 = v2GetBookSymbolR0 = Entry('book/{symbol}/R0', 'v2', 'GET', {'cost': 0.375})
    v2_get_stats1_key_size_symbol_side_section = v2GetStats1KeySizeSymbolSideSection = Entry('stats1/{key}:{size}:{symbol}:{side}/{section}', 'v2', 'GET', {'cost': 1})
    v2_get_stats1_key_size_symbol_section = v2GetStats1KeySizeSymbolSection = Entry('stats1/{key}:{size}:{symbol}/{section}', 'v2', 'GET', {'cost': 1})
    v2_get_stats1_key_size_symbol_long_last = v2GetStats1KeySizeSymbolLongLast = Entry('stats1/{key}:{size}:{symbol}:long/last', 'v2', 'GET', {'cost': 1})
    v2_get_stats1_key_size_symbol_long_hist = v2GetStats1KeySizeSymbolLongHist = Entry('stats1/{key}:{size}:{symbol}:long/hist', 'v2', 'GET', {'cost': 1})
    v2_get_stats1_key_size_symbol_short_last = v2GetStats1KeySizeSymbolShortLast = Entry('stats1/{key}:{size}:{symbol}:short/last', 'v2', 'GET', {'cost': 1})
    v2_get_stats1_key_size_symbol_short_hist = v2GetStats1KeySizeSymbolShortHist = Entry('stats1/{key}:{size}:{symbol}:short/hist', 'v2', 'GET', {'cost': 1})
    v2_get_candles_trade_timeframe_symbol_section = v2GetCandlesTradeTimeframeSymbolSection = Entry('candles/trade:{timeframe}:{symbol}/{section}', 'v2', 'GET', {'cost': 1})
    v2_get_candles_trade_timeframe_symbol_last = v2GetCandlesTradeTimeframeSymbolLast = Entry('candles/trade:{timeframe}:{symbol}/last', 'v2', 'GET', {'cost': 1})
    v2_get_candles_trade_timeframe_symbol_hist = v2GetCandlesTradeTimeframeSymbolHist = Entry('candles/trade:{timeframe}:{symbol}/hist', 'v2', 'GET', {'cost': 1})
    public_get_book_symbol = publicGetBookSymbol = Entry('book/{symbol}', 'public', 'GET', {'cost': 1})
    public_get_lendbook_currency = publicGetLendbookCurrency = Entry('lendbook/{currency}', 'public', 'GET', {'cost': 6})
    public_get_lends_currency = publicGetLendsCurrency = Entry('lends/{currency}', 'public', 'GET', {'cost': 3})
    public_get_pubticker_symbol = publicGetPubtickerSymbol = Entry('pubticker/{symbol}', 'public', 'GET', {'cost': 3})
    public_get_stats_symbol = publicGetStatsSymbol = Entry('stats/{symbol}', 'public', 'GET', {'cost': 6})
    public_get_symbols = publicGetSymbols = Entry('symbols', 'public', 'GET', {'cost': 18})
    public_get_symbols_details = publicGetSymbolsDetails = Entry('symbols_details', 'public', 'GET', {'cost': 18})
    public_get_tickers = publicGetTickers = Entry('tickers', 'public', 'GET', {'cost': 1})
    public_get_trades_symbol = publicGetTradesSymbol = Entry('trades/{symbol}', 'public', 'GET', {'cost': 3})
    private_post_account_fees = privatePostAccountFees = Entry('account_fees', 'private', 'POST', {'cost': 18})
    private_post_account_infos = privatePostAccountInfos = Entry('account_infos', 'private', 'POST', {'cost': 6})
    private_post_balances = privatePostBalances = Entry('balances', 'private', 'POST', {'cost': 9.036})
    private_post_basket_manage = privatePostBasketManage = Entry('basket_manage', 'private', 'POST', {'cost': 6})
    private_post_credits = privatePostCredits = Entry('credits', 'private', 'POST', {'cost': 6})
    private_post_deposit_new = privatePostDepositNew = Entry('deposit/new', 'private', 'POST', {'cost': 18})
    private_post_funding_close = privatePostFundingClose = Entry('funding/close', 'private', 'POST', {'cost': 6})
    private_post_history = privatePostHistory = Entry('history', 'private', 'POST', {'cost': 6})
    private_post_history_movements = privatePostHistoryMovements = Entry('history/movements', 'private', 'POST', {'cost': 6})
    private_post_key_info = privatePostKeyInfo = Entry('key_info', 'private', 'POST', {'cost': 6})
    private_post_margin_infos = privatePostMarginInfos = Entry('margin_infos', 'private', 'POST', {'cost': 3})
    private_post_mytrades = privatePostMytrades = Entry('mytrades', 'private', 'POST', {'cost': 3})
    private_post_mytrades_funding = privatePostMytradesFunding = Entry('mytrades_funding', 'private', 'POST', {'cost': 6})
    private_post_offer_cancel = privatePostOfferCancel = Entry('offer/cancel', 'private', 'POST', {'cost': 6})
    private_post_offer_new = privatePostOfferNew = Entry('offer/new', 'private', 'POST', {'cost': 6})
    private_post_offer_status = privatePostOfferStatus = Entry('offer/status', 'private', 'POST', {'cost': 6})
    private_post_offers = privatePostOffers = Entry('offers', 'private', 'POST', {'cost': 6})
    private_post_offers_hist = privatePostOffersHist = Entry('offers/hist', 'private', 'POST', {'cost': 90.03})
    private_post_order_cancel = privatePostOrderCancel = Entry('order/cancel', 'private', 'POST', {'cost': 0.2})
    private_post_order_cancel_all = privatePostOrderCancelAll = Entry('order/cancel/all', 'private', 'POST', {'cost': 0.2})
    private_post_order_cancel_multi = privatePostOrderCancelMulti = Entry('order/cancel/multi', 'private', 'POST', {'cost': 0.2})
    private_post_order_cancel_replace = privatePostOrderCancelReplace = Entry('order/cancel/replace', 'private', 'POST', {'cost': 0.2})
    private_post_order_new = privatePostOrderNew = Entry('order/new', 'private', 'POST', {'cost': 0.2})
    private_post_order_new_multi = privatePostOrderNewMulti = Entry('order/new/multi', 'private', 'POST', {'cost': 0.2})
    private_post_order_status = privatePostOrderStatus = Entry('order/status', 'private', 'POST', {'cost': 0.2})
    private_post_orders = privatePostOrders = Entry('orders', 'private', 'POST', {'cost': 0.2})
    private_post_orders_hist = privatePostOrdersHist = Entry('orders/hist', 'private', 'POST', {'cost': 90.03})
    private_post_position_claim = privatePostPositionClaim = Entry('position/claim', 'private', 'POST', {'cost': 18})
    private_post_position_close = privatePostPositionClose = Entry('position/close', 'private', 'POST', {'cost': 18})
    private_post_positions = privatePostPositions = Entry('positions', 'private', 'POST', {'cost': 18})
    private_post_summary = privatePostSummary = Entry('summary', 'private', 'POST', {'cost': 18})
    private_post_taken_funds = privatePostTakenFunds = Entry('taken_funds', 'private', 'POST', {'cost': 6})
    private_post_total_taken_funds = privatePostTotalTakenFunds = Entry('total_taken_funds', 'private', 'POST', {'cost': 6})
    private_post_transfer = privatePostTransfer = Entry('transfer', 'private', 'POST', {'cost': 18})
    private_post_unused_taken_funds = privatePostUnusedTakenFunds = Entry('unused_taken_funds', 'private', 'POST', {'cost': 6})
    private_post_withdraw = privatePostWithdraw = Entry('withdraw', 'private', 'POST', {'cost': 18})
