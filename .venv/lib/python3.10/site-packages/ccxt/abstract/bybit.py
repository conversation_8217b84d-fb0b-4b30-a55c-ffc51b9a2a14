from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_spot_v3_public_symbols = publicGetSpotV3PublicSymbols = Entry('spot/v3/public/symbols', 'public', 'GET', {'cost': 1})
    public_get_spot_v3_public_quote_depth = publicGetSpotV3PublicQuoteDepth = Entry('spot/v3/public/quote/depth', 'public', 'GET', {'cost': 1})
    public_get_spot_v3_public_quote_depth_merged = publicGetSpotV3PublicQuoteDepthMerged = Entry('spot/v3/public/quote/depth/merged', 'public', 'GET', {'cost': 1})
    public_get_spot_v3_public_quote_trades = publicGetSpotV3PublicQuoteTrades = Entry('spot/v3/public/quote/trades', 'public', 'GET', {'cost': 1})
    public_get_spot_v3_public_quote_kline = publicGetSpotV3PublicQuoteKline = Entry('spot/v3/public/quote/kline', 'public', 'GET', {'cost': 1})
    public_get_spot_v3_public_quote_ticker_24hr = publicGetSpotV3PublicQuoteTicker24hr = Entry('spot/v3/public/quote/ticker/24hr', 'public', 'GET', {'cost': 1})
    public_get_spot_v3_public_quote_ticker_price = publicGetSpotV3PublicQuoteTickerPrice = Entry('spot/v3/public/quote/ticker/price', 'public', 'GET', {'cost': 1})
    public_get_spot_v3_public_quote_ticker_bookticker = publicGetSpotV3PublicQuoteTickerBookTicker = Entry('spot/v3/public/quote/ticker/bookTicker', 'public', 'GET', {'cost': 1})
    public_get_spot_v3_public_server_time = publicGetSpotV3PublicServerTime = Entry('spot/v3/public/server-time', 'public', 'GET', {'cost': 1})
    public_get_spot_v3_public_infos = publicGetSpotV3PublicInfos = Entry('spot/v3/public/infos', 'public', 'GET', {'cost': 1})
    public_get_spot_v3_public_margin_product_infos = publicGetSpotV3PublicMarginProductInfos = Entry('spot/v3/public/margin-product-infos', 'public', 'GET', {'cost': 1})
    public_get_spot_v3_public_margin_ensure_tokens = publicGetSpotV3PublicMarginEnsureTokens = Entry('spot/v3/public/margin-ensure-tokens', 'public', 'GET', {'cost': 1})
    public_get_v3_public_time = publicGetV3PublicTime = Entry('v3/public/time', 'public', 'GET', {'cost': 1})
    public_get_contract_v3_public_copytrading_symbol_list = publicGetContractV3PublicCopytradingSymbolList = Entry('contract/v3/public/copytrading/symbol/list', 'public', 'GET', {'cost': 1})
    public_get_derivatives_v3_public_order_book_l2 = publicGetDerivativesV3PublicOrderBookL2 = Entry('derivatives/v3/public/order-book/L2', 'public', 'GET', {'cost': 1})
    public_get_derivatives_v3_public_kline = publicGetDerivativesV3PublicKline = Entry('derivatives/v3/public/kline', 'public', 'GET', {'cost': 1})
    public_get_derivatives_v3_public_tickers = publicGetDerivativesV3PublicTickers = Entry('derivatives/v3/public/tickers', 'public', 'GET', {'cost': 1})
    public_get_derivatives_v3_public_instruments_info = publicGetDerivativesV3PublicInstrumentsInfo = Entry('derivatives/v3/public/instruments-info', 'public', 'GET', {'cost': 1})
    public_get_derivatives_v3_public_mark_price_kline = publicGetDerivativesV3PublicMarkPriceKline = Entry('derivatives/v3/public/mark-price-kline', 'public', 'GET', {'cost': 1})
    public_get_derivatives_v3_public_index_price_kline = publicGetDerivativesV3PublicIndexPriceKline = Entry('derivatives/v3/public/index-price-kline', 'public', 'GET', {'cost': 1})
    public_get_derivatives_v3_public_funding_history_funding_rate = publicGetDerivativesV3PublicFundingHistoryFundingRate = Entry('derivatives/v3/public/funding/history-funding-rate', 'public', 'GET', {'cost': 1})
    public_get_derivatives_v3_public_risk_limit_list = publicGetDerivativesV3PublicRiskLimitList = Entry('derivatives/v3/public/risk-limit/list', 'public', 'GET', {'cost': 1})
    public_get_derivatives_v3_public_delivery_price = publicGetDerivativesV3PublicDeliveryPrice = Entry('derivatives/v3/public/delivery-price', 'public', 'GET', {'cost': 1})
    public_get_derivatives_v3_public_recent_trade = publicGetDerivativesV3PublicRecentTrade = Entry('derivatives/v3/public/recent-trade', 'public', 'GET', {'cost': 1})
    public_get_derivatives_v3_public_open_interest = publicGetDerivativesV3PublicOpenInterest = Entry('derivatives/v3/public/open-interest', 'public', 'GET', {'cost': 1})
    public_get_derivatives_v3_public_insurance = publicGetDerivativesV3PublicInsurance = Entry('derivatives/v3/public/insurance', 'public', 'GET', {'cost': 1})
    public_get_v5_announcements_index = publicGetV5AnnouncementsIndex = Entry('v5/announcements/index', 'public', 'GET', {'cost': 5})
    public_get_v5_market_time = publicGetV5MarketTime = Entry('v5/market/time', 'public', 'GET', {'cost': 5})
    public_get_v5_market_kline = publicGetV5MarketKline = Entry('v5/market/kline', 'public', 'GET', {'cost': 5})
    public_get_v5_market_mark_price_kline = publicGetV5MarketMarkPriceKline = Entry('v5/market/mark-price-kline', 'public', 'GET', {'cost': 5})
    public_get_v5_market_index_price_kline = publicGetV5MarketIndexPriceKline = Entry('v5/market/index-price-kline', 'public', 'GET', {'cost': 5})
    public_get_v5_market_premium_index_price_kline = publicGetV5MarketPremiumIndexPriceKline = Entry('v5/market/premium-index-price-kline', 'public', 'GET', {'cost': 5})
    public_get_v5_market_instruments_info = publicGetV5MarketInstrumentsInfo = Entry('v5/market/instruments-info', 'public', 'GET', {'cost': 5})
    public_get_v5_market_orderbook = publicGetV5MarketOrderbook = Entry('v5/market/orderbook', 'public', 'GET', {'cost': 5})
    public_get_v5_market_tickers = publicGetV5MarketTickers = Entry('v5/market/tickers', 'public', 'GET', {'cost': 5})
    public_get_v5_market_funding_history = publicGetV5MarketFundingHistory = Entry('v5/market/funding/history', 'public', 'GET', {'cost': 5})
    public_get_v5_market_recent_trade = publicGetV5MarketRecentTrade = Entry('v5/market/recent-trade', 'public', 'GET', {'cost': 5})
    public_get_v5_market_open_interest = publicGetV5MarketOpenInterest = Entry('v5/market/open-interest', 'public', 'GET', {'cost': 5})
    public_get_v5_market_historical_volatility = publicGetV5MarketHistoricalVolatility = Entry('v5/market/historical-volatility', 'public', 'GET', {'cost': 5})
    public_get_v5_market_insurance = publicGetV5MarketInsurance = Entry('v5/market/insurance', 'public', 'GET', {'cost': 5})
    public_get_v5_market_risk_limit = publicGetV5MarketRiskLimit = Entry('v5/market/risk-limit', 'public', 'GET', {'cost': 5})
    public_get_v5_market_delivery_price = publicGetV5MarketDeliveryPrice = Entry('v5/market/delivery-price', 'public', 'GET', {'cost': 5})
    public_get_v5_market_account_ratio = publicGetV5MarketAccountRatio = Entry('v5/market/account-ratio', 'public', 'GET', {'cost': 5})
    public_get_v5_spot_lever_token_info = publicGetV5SpotLeverTokenInfo = Entry('v5/spot-lever-token/info', 'public', 'GET', {'cost': 5})
    public_get_v5_spot_lever_token_reference = publicGetV5SpotLeverTokenReference = Entry('v5/spot-lever-token/reference', 'public', 'GET', {'cost': 5})
    public_get_v5_spot_margin_trade_data = publicGetV5SpotMarginTradeData = Entry('v5/spot-margin-trade/data', 'public', 'GET', {'cost': 5})
    public_get_v5_spot_cross_margin_trade_data = publicGetV5SpotCrossMarginTradeData = Entry('v5/spot-cross-margin-trade/data', 'public', 'GET', {'cost': 5})
    public_get_v5_spot_cross_margin_trade_pledge_token = publicGetV5SpotCrossMarginTradePledgeToken = Entry('v5/spot-cross-margin-trade/pledge-token', 'public', 'GET', {'cost': 5})
    public_get_v5_spot_cross_margin_trade_borrow_token = publicGetV5SpotCrossMarginTradeBorrowToken = Entry('v5/spot-cross-margin-trade/borrow-token', 'public', 'GET', {'cost': 5})
    public_get_v5_ins_loan_product_infos = publicGetV5InsLoanProductInfos = Entry('v5/ins-loan/product-infos', 'public', 'GET', {'cost': 5})
    public_get_v5_ins_loan_ensure_tokens_convert = publicGetV5InsLoanEnsureTokensConvert = Entry('v5/ins-loan/ensure-tokens-convert', 'public', 'GET', {'cost': 5})
    private_get_v2_private_wallet_fund_records = privateGetV2PrivateWalletFundRecords = Entry('v2/private/wallet/fund/records', 'private', 'GET', {'cost': 25})
    private_get_spot_v3_private_order = privateGetSpotV3PrivateOrder = Entry('spot/v3/private/order', 'private', 'GET', {'cost': 2.5})
    private_get_spot_v3_private_open_orders = privateGetSpotV3PrivateOpenOrders = Entry('spot/v3/private/open-orders', 'private', 'GET', {'cost': 2.5})
    private_get_spot_v3_private_history_orders = privateGetSpotV3PrivateHistoryOrders = Entry('spot/v3/private/history-orders', 'private', 'GET', {'cost': 2.5})
    private_get_spot_v3_private_my_trades = privateGetSpotV3PrivateMyTrades = Entry('spot/v3/private/my-trades', 'private', 'GET', {'cost': 2.5})
    private_get_spot_v3_private_account = privateGetSpotV3PrivateAccount = Entry('spot/v3/private/account', 'private', 'GET', {'cost': 2.5})
    private_get_spot_v3_private_reference = privateGetSpotV3PrivateReference = Entry('spot/v3/private/reference', 'private', 'GET', {'cost': 2.5})
    private_get_spot_v3_private_record = privateGetSpotV3PrivateRecord = Entry('spot/v3/private/record', 'private', 'GET', {'cost': 2.5})
    private_get_spot_v3_private_cross_margin_orders = privateGetSpotV3PrivateCrossMarginOrders = Entry('spot/v3/private/cross-margin-orders', 'private', 'GET', {'cost': 10})
    private_get_spot_v3_private_cross_margin_account = privateGetSpotV3PrivateCrossMarginAccount = Entry('spot/v3/private/cross-margin-account', 'private', 'GET', {'cost': 10})
    private_get_spot_v3_private_cross_margin_loan_info = privateGetSpotV3PrivateCrossMarginLoanInfo = Entry('spot/v3/private/cross-margin-loan-info', 'private', 'GET', {'cost': 10})
    private_get_spot_v3_private_cross_margin_repay_history = privateGetSpotV3PrivateCrossMarginRepayHistory = Entry('spot/v3/private/cross-margin-repay-history', 'private', 'GET', {'cost': 10})
    private_get_spot_v3_private_margin_loan_infos = privateGetSpotV3PrivateMarginLoanInfos = Entry('spot/v3/private/margin-loan-infos', 'private', 'GET', {'cost': 10})
    private_get_spot_v3_private_margin_repaid_infos = privateGetSpotV3PrivateMarginRepaidInfos = Entry('spot/v3/private/margin-repaid-infos', 'private', 'GET', {'cost': 10})
    private_get_spot_v3_private_margin_ltv = privateGetSpotV3PrivateMarginLtv = Entry('spot/v3/private/margin-ltv', 'private', 'GET', {'cost': 10})
    private_get_asset_v3_private_transfer_inter_transfer_list_query = privateGetAssetV3PrivateTransferInterTransferListQuery = Entry('asset/v3/private/transfer/inter-transfer/list/query', 'private', 'GET', {'cost': 50})
    private_get_asset_v3_private_transfer_sub_member_list_query = privateGetAssetV3PrivateTransferSubMemberListQuery = Entry('asset/v3/private/transfer/sub-member/list/query', 'private', 'GET', {'cost': 50})
    private_get_asset_v3_private_transfer_sub_member_transfer_list_query = privateGetAssetV3PrivateTransferSubMemberTransferListQuery = Entry('asset/v3/private/transfer/sub-member-transfer/list/query', 'private', 'GET', {'cost': 50})
    private_get_asset_v3_private_transfer_universal_transfer_list_query = privateGetAssetV3PrivateTransferUniversalTransferListQuery = Entry('asset/v3/private/transfer/universal-transfer/list/query', 'private', 'GET', {'cost': 25})
    private_get_asset_v3_private_coin_info_query = privateGetAssetV3PrivateCoinInfoQuery = Entry('asset/v3/private/coin-info/query', 'private', 'GET', {'cost': 25})
    private_get_asset_v3_private_deposit_address_query = privateGetAssetV3PrivateDepositAddressQuery = Entry('asset/v3/private/deposit/address/query', 'private', 'GET', {'cost': 10})
    private_get_contract_v3_private_copytrading_order_list = privateGetContractV3PrivateCopytradingOrderList = Entry('contract/v3/private/copytrading/order/list', 'private', 'GET', {'cost': 30})
    private_get_contract_v3_private_copytrading_position_list = privateGetContractV3PrivateCopytradingPositionList = Entry('contract/v3/private/copytrading/position/list', 'private', 'GET', {'cost': 40})
    private_get_contract_v3_private_copytrading_wallet_balance = privateGetContractV3PrivateCopytradingWalletBalance = Entry('contract/v3/private/copytrading/wallet/balance', 'private', 'GET', {'cost': 25})
    private_get_contract_v3_private_position_limit_info = privateGetContractV3PrivatePositionLimitInfo = Entry('contract/v3/private/position/limit-info', 'private', 'GET', {'cost': 25})
    private_get_contract_v3_private_order_unfilled_orders = privateGetContractV3PrivateOrderUnfilledOrders = Entry('contract/v3/private/order/unfilled-orders', 'private', 'GET', {'cost': 1})
    private_get_contract_v3_private_order_list = privateGetContractV3PrivateOrderList = Entry('contract/v3/private/order/list', 'private', 'GET', {'cost': 1})
    private_get_contract_v3_private_position_list = privateGetContractV3PrivatePositionList = Entry('contract/v3/private/position/list', 'private', 'GET', {'cost': 1})
    private_get_contract_v3_private_execution_list = privateGetContractV3PrivateExecutionList = Entry('contract/v3/private/execution/list', 'private', 'GET', {'cost': 1})
    private_get_contract_v3_private_position_closed_pnl = privateGetContractV3PrivatePositionClosedPnl = Entry('contract/v3/private/position/closed-pnl', 'private', 'GET', {'cost': 1})
    private_get_contract_v3_private_account_wallet_balance = privateGetContractV3PrivateAccountWalletBalance = Entry('contract/v3/private/account/wallet/balance', 'private', 'GET', {'cost': 1})
    private_get_contract_v3_private_account_fee_rate = privateGetContractV3PrivateAccountFeeRate = Entry('contract/v3/private/account/fee-rate', 'private', 'GET', {'cost': 1})
    private_get_contract_v3_private_account_wallet_fund_records = privateGetContractV3PrivateAccountWalletFundRecords = Entry('contract/v3/private/account/wallet/fund-records', 'private', 'GET', {'cost': 1})
    private_get_unified_v3_private_order_unfilled_orders = privateGetUnifiedV3PrivateOrderUnfilledOrders = Entry('unified/v3/private/order/unfilled-orders', 'private', 'GET', {'cost': 1})
    private_get_unified_v3_private_order_list = privateGetUnifiedV3PrivateOrderList = Entry('unified/v3/private/order/list', 'private', 'GET', {'cost': 1})
    private_get_unified_v3_private_position_list = privateGetUnifiedV3PrivatePositionList = Entry('unified/v3/private/position/list', 'private', 'GET', {'cost': 1})
    private_get_unified_v3_private_execution_list = privateGetUnifiedV3PrivateExecutionList = Entry('unified/v3/private/execution/list', 'private', 'GET', {'cost': 1})
    private_get_unified_v3_private_delivery_record = privateGetUnifiedV3PrivateDeliveryRecord = Entry('unified/v3/private/delivery-record', 'private', 'GET', {'cost': 1})
    private_get_unified_v3_private_settlement_record = privateGetUnifiedV3PrivateSettlementRecord = Entry('unified/v3/private/settlement-record', 'private', 'GET', {'cost': 1})
    private_get_unified_v3_private_account_wallet_balance = privateGetUnifiedV3PrivateAccountWalletBalance = Entry('unified/v3/private/account/wallet/balance', 'private', 'GET', {'cost': 1})
    private_get_unified_v3_private_account_transaction_log = privateGetUnifiedV3PrivateAccountTransactionLog = Entry('unified/v3/private/account/transaction-log', 'private', 'GET', {'cost': 1})
    private_get_unified_v3_private_account_borrow_history = privateGetUnifiedV3PrivateAccountBorrowHistory = Entry('unified/v3/private/account/borrow-history', 'private', 'GET', {'cost': 1})
    private_get_unified_v3_private_account_borrow_rate = privateGetUnifiedV3PrivateAccountBorrowRate = Entry('unified/v3/private/account/borrow-rate', 'private', 'GET', {'cost': 1})
    private_get_unified_v3_private_account_info = privateGetUnifiedV3PrivateAccountInfo = Entry('unified/v3/private/account/info', 'private', 'GET', {'cost': 1})
    private_get_user_v3_private_frozen_sub_member = privateGetUserV3PrivateFrozenSubMember = Entry('user/v3/private/frozen-sub-member', 'private', 'GET', {'cost': 10})
    private_get_user_v3_private_query_sub_members = privateGetUserV3PrivateQuerySubMembers = Entry('user/v3/private/query-sub-members', 'private', 'GET', {'cost': 5})
    private_get_user_v3_private_query_api = privateGetUserV3PrivateQueryApi = Entry('user/v3/private/query-api', 'private', 'GET', {'cost': 5})
    private_get_user_v3_private_get_member_type = privateGetUserV3PrivateGetMemberType = Entry('user/v3/private/get-member-type', 'private', 'GET', {'cost': 1})
    private_get_asset_v3_private_transfer_transfer_coin_list_query = privateGetAssetV3PrivateTransferTransferCoinListQuery = Entry('asset/v3/private/transfer/transfer-coin/list/query', 'private', 'GET', {'cost': 50})
    private_get_asset_v3_private_transfer_account_coin_balance_query = privateGetAssetV3PrivateTransferAccountCoinBalanceQuery = Entry('asset/v3/private/transfer/account-coin/balance/query', 'private', 'GET', {'cost': 50})
    private_get_asset_v3_private_transfer_account_coins_balance_query = privateGetAssetV3PrivateTransferAccountCoinsBalanceQuery = Entry('asset/v3/private/transfer/account-coins/balance/query', 'private', 'GET', {'cost': 25})
    private_get_asset_v3_private_transfer_asset_info_query = privateGetAssetV3PrivateTransferAssetInfoQuery = Entry('asset/v3/private/transfer/asset-info/query', 'private', 'GET', {'cost': 50})
    private_get_asset_v3_public_deposit_allowed_deposit_list_query = privateGetAssetV3PublicDepositAllowedDepositListQuery = Entry('asset/v3/public/deposit/allowed-deposit-list/query', 'private', 'GET', {'cost': 0.17})
    private_get_asset_v3_private_deposit_record_query = privateGetAssetV3PrivateDepositRecordQuery = Entry('asset/v3/private/deposit/record/query', 'private', 'GET', {'cost': 10})
    private_get_asset_v3_private_withdraw_record_query = privateGetAssetV3PrivateWithdrawRecordQuery = Entry('asset/v3/private/withdraw/record/query', 'private', 'GET', {'cost': 10})
    private_get_v5_order_realtime = privateGetV5OrderRealtime = Entry('v5/order/realtime', 'private', 'GET', {'cost': 5})
    private_get_v5_order_history = privateGetV5OrderHistory = Entry('v5/order/history', 'private', 'GET', {'cost': 5})
    private_get_v5_order_spot_borrow_check = privateGetV5OrderSpotBorrowCheck = Entry('v5/order/spot-borrow-check', 'private', 'GET', {'cost': 1})
    private_get_v5_position_list = privateGetV5PositionList = Entry('v5/position/list', 'private', 'GET', {'cost': 5})
    private_get_v5_execution_list = privateGetV5ExecutionList = Entry('v5/execution/list', 'private', 'GET', {'cost': 5})
    private_get_v5_position_closed_pnl = privateGetV5PositionClosedPnl = Entry('v5/position/closed-pnl', 'private', 'GET', {'cost': 5})
    private_get_v5_position_move_history = privateGetV5PositionMoveHistory = Entry('v5/position/move-history', 'private', 'GET', {'cost': 5})
    private_get_v5_pre_upgrade_order_history = privateGetV5PreUpgradeOrderHistory = Entry('v5/pre-upgrade/order/history', 'private', 'GET', {'cost': 5})
    private_get_v5_pre_upgrade_execution_list = privateGetV5PreUpgradeExecutionList = Entry('v5/pre-upgrade/execution/list', 'private', 'GET', {'cost': 5})
    private_get_v5_pre_upgrade_position_closed_pnl = privateGetV5PreUpgradePositionClosedPnl = Entry('v5/pre-upgrade/position/closed-pnl', 'private', 'GET', {'cost': 5})
    private_get_v5_pre_upgrade_account_transaction_log = privateGetV5PreUpgradeAccountTransactionLog = Entry('v5/pre-upgrade/account/transaction-log', 'private', 'GET', {'cost': 5})
    private_get_v5_pre_upgrade_asset_delivery_record = privateGetV5PreUpgradeAssetDeliveryRecord = Entry('v5/pre-upgrade/asset/delivery-record', 'private', 'GET', {'cost': 5})
    private_get_v5_pre_upgrade_asset_settlement_record = privateGetV5PreUpgradeAssetSettlementRecord = Entry('v5/pre-upgrade/asset/settlement-record', 'private', 'GET', {'cost': 5})
    private_get_v5_account_wallet_balance = privateGetV5AccountWalletBalance = Entry('v5/account/wallet-balance', 'private', 'GET', {'cost': 1})
    private_get_v5_account_borrow_history = privateGetV5AccountBorrowHistory = Entry('v5/account/borrow-history', 'private', 'GET', {'cost': 1})
    private_get_v5_account_collateral_info = privateGetV5AccountCollateralInfo = Entry('v5/account/collateral-info', 'private', 'GET', {'cost': 1})
    private_get_v5_asset_coin_greeks = privateGetV5AssetCoinGreeks = Entry('v5/asset/coin-greeks', 'private', 'GET', {'cost': 1})
    private_get_v5_account_fee_rate = privateGetV5AccountFeeRate = Entry('v5/account/fee-rate', 'private', 'GET', {'cost': 10})
    private_get_v5_account_info = privateGetV5AccountInfo = Entry('v5/account/info', 'private', 'GET', {'cost': 5})
    private_get_v5_account_transaction_log = privateGetV5AccountTransactionLog = Entry('v5/account/transaction-log', 'private', 'GET', {'cost': 1})
    private_get_v5_account_contract_transaction_log = privateGetV5AccountContractTransactionLog = Entry('v5/account/contract-transaction-log', 'private', 'GET', {'cost': 1})
    private_get_v5_account_smp_group = privateGetV5AccountSmpGroup = Entry('v5/account/smp-group', 'private', 'GET', {'cost': 1})
    private_get_v5_account_mmp_state = privateGetV5AccountMmpState = Entry('v5/account/mmp-state', 'private', 'GET', {'cost': 5})
    private_get_v5_asset_exchange_order_record = privateGetV5AssetExchangeOrderRecord = Entry('v5/asset/exchange/order-record', 'private', 'GET', {'cost': 5})
    private_get_v5_asset_delivery_record = privateGetV5AssetDeliveryRecord = Entry('v5/asset/delivery-record', 'private', 'GET', {'cost': 5})
    private_get_v5_asset_settlement_record = privateGetV5AssetSettlementRecord = Entry('v5/asset/settlement-record', 'private', 'GET', {'cost': 5})
    private_get_v5_asset_transfer_query_asset_info = privateGetV5AssetTransferQueryAssetInfo = Entry('v5/asset/transfer/query-asset-info', 'private', 'GET', {'cost': 50})
    private_get_v5_asset_transfer_query_account_coins_balance = privateGetV5AssetTransferQueryAccountCoinsBalance = Entry('v5/asset/transfer/query-account-coins-balance', 'private', 'GET', {'cost': 25})
    private_get_v5_asset_transfer_query_account_coin_balance = privateGetV5AssetTransferQueryAccountCoinBalance = Entry('v5/asset/transfer/query-account-coin-balance', 'private', 'GET', {'cost': 50})
    private_get_v5_asset_transfer_query_transfer_coin_list = privateGetV5AssetTransferQueryTransferCoinList = Entry('v5/asset/transfer/query-transfer-coin-list', 'private', 'GET', {'cost': 50})
    private_get_v5_asset_transfer_query_inter_transfer_list = privateGetV5AssetTransferQueryInterTransferList = Entry('v5/asset/transfer/query-inter-transfer-list', 'private', 'GET', {'cost': 50})
    private_get_v5_asset_transfer_query_sub_member_list = privateGetV5AssetTransferQuerySubMemberList = Entry('v5/asset/transfer/query-sub-member-list', 'private', 'GET', {'cost': 50})
    private_get_v5_asset_transfer_query_universal_transfer_list = privateGetV5AssetTransferQueryUniversalTransferList = Entry('v5/asset/transfer/query-universal-transfer-list', 'private', 'GET', {'cost': 25})
    private_get_v5_asset_deposit_query_allowed_list = privateGetV5AssetDepositQueryAllowedList = Entry('v5/asset/deposit/query-allowed-list', 'private', 'GET', {'cost': 5})
    private_get_v5_asset_deposit_query_record = privateGetV5AssetDepositQueryRecord = Entry('v5/asset/deposit/query-record', 'private', 'GET', {'cost': 10})
    private_get_v5_asset_deposit_query_sub_member_record = privateGetV5AssetDepositQuerySubMemberRecord = Entry('v5/asset/deposit/query-sub-member-record', 'private', 'GET', {'cost': 10})
    private_get_v5_asset_deposit_query_internal_record = privateGetV5AssetDepositQueryInternalRecord = Entry('v5/asset/deposit/query-internal-record', 'private', 'GET', {'cost': 5})
    private_get_v5_asset_deposit_query_address = privateGetV5AssetDepositQueryAddress = Entry('v5/asset/deposit/query-address', 'private', 'GET', {'cost': 10})
    private_get_v5_asset_deposit_query_sub_member_address = privateGetV5AssetDepositQuerySubMemberAddress = Entry('v5/asset/deposit/query-sub-member-address', 'private', 'GET', {'cost': 10})
    private_get_v5_asset_coin_query_info = privateGetV5AssetCoinQueryInfo = Entry('v5/asset/coin/query-info', 'private', 'GET', {'cost': 28})
    private_get_v5_asset_withdraw_query_record = privateGetV5AssetWithdrawQueryRecord = Entry('v5/asset/withdraw/query-record', 'private', 'GET', {'cost': 10})
    private_get_v5_asset_withdraw_withdrawable_amount = privateGetV5AssetWithdrawWithdrawableAmount = Entry('v5/asset/withdraw/withdrawable-amount', 'private', 'GET', {'cost': 5})
    private_get_v5_asset_withdraw_vasp_list = privateGetV5AssetWithdrawVaspList = Entry('v5/asset/withdraw/vasp/list', 'private', 'GET', {'cost': 5})
    private_get_v5_user_query_sub_members = privateGetV5UserQuerySubMembers = Entry('v5/user/query-sub-members', 'private', 'GET', {'cost': 5})
    private_get_v5_user_query_api = privateGetV5UserQueryApi = Entry('v5/user/query-api', 'private', 'GET', {'cost': 5})
    private_get_v5_user_sub_apikeys = privateGetV5UserSubApikeys = Entry('v5/user/sub-apikeys', 'private', 'GET', {'cost': 5})
    private_get_v5_user_get_member_type = privateGetV5UserGetMemberType = Entry('v5/user/get-member-type', 'private', 'GET', {'cost': 5})
    private_get_v5_user_aff_customer_info = privateGetV5UserAffCustomerInfo = Entry('v5/user/aff-customer-info', 'private', 'GET', {'cost': 5})
    private_get_v5_user_del_submember = privateGetV5UserDelSubmember = Entry('v5/user/del-submember', 'private', 'GET', {'cost': 5})
    private_get_v5_user_submembers = privateGetV5UserSubmembers = Entry('v5/user/submembers', 'private', 'GET', {'cost': 5})
    private_get_v5_spot_lever_token_order_record = privateGetV5SpotLeverTokenOrderRecord = Entry('v5/spot-lever-token/order-record', 'private', 'GET', {'cost': 1})
    private_get_v5_spot_margin_trade_state = privateGetV5SpotMarginTradeState = Entry('v5/spot-margin-trade/state', 'private', 'GET', {'cost': 5})
    private_get_v5_spot_cross_margin_trade_loan_info = privateGetV5SpotCrossMarginTradeLoanInfo = Entry('v5/spot-cross-margin-trade/loan-info', 'private', 'GET', {'cost': 1})
    private_get_v5_spot_cross_margin_trade_account = privateGetV5SpotCrossMarginTradeAccount = Entry('v5/spot-cross-margin-trade/account', 'private', 'GET', {'cost': 1})
    private_get_v5_spot_cross_margin_trade_orders = privateGetV5SpotCrossMarginTradeOrders = Entry('v5/spot-cross-margin-trade/orders', 'private', 'GET', {'cost': 1})
    private_get_v5_spot_cross_margin_trade_repay_history = privateGetV5SpotCrossMarginTradeRepayHistory = Entry('v5/spot-cross-margin-trade/repay-history', 'private', 'GET', {'cost': 1})
    private_get_v5_ins_loan_product_infos = privateGetV5InsLoanProductInfos = Entry('v5/ins-loan/product-infos', 'private', 'GET', {'cost': 5})
    private_get_v5_ins_loan_ensure_tokens_convert = privateGetV5InsLoanEnsureTokensConvert = Entry('v5/ins-loan/ensure-tokens-convert', 'private', 'GET', {'cost': 5})
    private_get_v5_ins_loan_loan_order = privateGetV5InsLoanLoanOrder = Entry('v5/ins-loan/loan-order', 'private', 'GET', {'cost': 5})
    private_get_v5_ins_loan_repaid_history = privateGetV5InsLoanRepaidHistory = Entry('v5/ins-loan/repaid-history', 'private', 'GET', {'cost': 5})
    private_get_v5_ins_loan_ltv_convert = privateGetV5InsLoanLtvConvert = Entry('v5/ins-loan/ltv-convert', 'private', 'GET', {'cost': 5})
    private_get_v5_lending_info = privateGetV5LendingInfo = Entry('v5/lending/info', 'private', 'GET', {'cost': 5})
    private_get_v5_lending_history_order = privateGetV5LendingHistoryOrder = Entry('v5/lending/history-order', 'private', 'GET', {'cost': 5})
    private_get_v5_lending_account = privateGetV5LendingAccount = Entry('v5/lending/account', 'private', 'GET', {'cost': 5})
    private_get_v5_broker_earning_record = privateGetV5BrokerEarningRecord = Entry('v5/broker/earning-record', 'private', 'GET', {'cost': 5})
    private_get_v5_broker_earnings_info = privateGetV5BrokerEarningsInfo = Entry('v5/broker/earnings-info', 'private', 'GET', {'cost': 5})
    private_get_v5_broker_account_info = privateGetV5BrokerAccountInfo = Entry('v5/broker/account-info', 'private', 'GET', {'cost': 5})
    private_get_v5_broker_asset_query_sub_member_deposit_record = privateGetV5BrokerAssetQuerySubMemberDepositRecord = Entry('v5/broker/asset/query-sub-member-deposit-record', 'private', 'GET', {'cost': 10})
    private_post_option_usdc_openapi_private_v1_place_order = privatePostOptionUsdcOpenapiPrivateV1PlaceOrder = Entry('option/usdc/openapi/private/v1/place-order', 'private', 'POST', {'cost': 2.5})
    private_post_option_usdc_openapi_private_v1_replace_order = privatePostOptionUsdcOpenapiPrivateV1ReplaceOrder = Entry('option/usdc/openapi/private/v1/replace-order', 'private', 'POST', {'cost': 2.5})
    private_post_option_usdc_openapi_private_v1_cancel_order = privatePostOptionUsdcOpenapiPrivateV1CancelOrder = Entry('option/usdc/openapi/private/v1/cancel-order', 'private', 'POST', {'cost': 2.5})
    private_post_option_usdc_openapi_private_v1_cancel_all = privatePostOptionUsdcOpenapiPrivateV1CancelAll = Entry('option/usdc/openapi/private/v1/cancel-all', 'private', 'POST', {'cost': 2.5})
    private_post_option_usdc_openapi_private_v1_query_active_orders = privatePostOptionUsdcOpenapiPrivateV1QueryActiveOrders = Entry('option/usdc/openapi/private/v1/query-active-orders', 'private', 'POST', {'cost': 2.5})
    private_post_option_usdc_openapi_private_v1_query_order_history = privatePostOptionUsdcOpenapiPrivateV1QueryOrderHistory = Entry('option/usdc/openapi/private/v1/query-order-history', 'private', 'POST', {'cost': 2.5})
    private_post_option_usdc_openapi_private_v1_execution_list = privatePostOptionUsdcOpenapiPrivateV1ExecutionList = Entry('option/usdc/openapi/private/v1/execution-list', 'private', 'POST', {'cost': 2.5})
    private_post_option_usdc_openapi_private_v1_query_position = privatePostOptionUsdcOpenapiPrivateV1QueryPosition = Entry('option/usdc/openapi/private/v1/query-position', 'private', 'POST', {'cost': 2.5})
    private_post_perpetual_usdc_openapi_private_v1_place_order = privatePostPerpetualUsdcOpenapiPrivateV1PlaceOrder = Entry('perpetual/usdc/openapi/private/v1/place-order', 'private', 'POST', {'cost': 2.5})
    private_post_perpetual_usdc_openapi_private_v1_replace_order = privatePostPerpetualUsdcOpenapiPrivateV1ReplaceOrder = Entry('perpetual/usdc/openapi/private/v1/replace-order', 'private', 'POST', {'cost': 2.5})
    private_post_perpetual_usdc_openapi_private_v1_cancel_order = privatePostPerpetualUsdcOpenapiPrivateV1CancelOrder = Entry('perpetual/usdc/openapi/private/v1/cancel-order', 'private', 'POST', {'cost': 2.5})
    private_post_perpetual_usdc_openapi_private_v1_cancel_all = privatePostPerpetualUsdcOpenapiPrivateV1CancelAll = Entry('perpetual/usdc/openapi/private/v1/cancel-all', 'private', 'POST', {'cost': 2.5})
    private_post_perpetual_usdc_openapi_private_v1_position_leverage_save = privatePostPerpetualUsdcOpenapiPrivateV1PositionLeverageSave = Entry('perpetual/usdc/openapi/private/v1/position/leverage/save', 'private', 'POST', {'cost': 2.5})
    private_post_spot_v3_private_order = privatePostSpotV3PrivateOrder = Entry('spot/v3/private/order', 'private', 'POST', {'cost': 2.5})
    private_post_spot_v3_private_cancel_order = privatePostSpotV3PrivateCancelOrder = Entry('spot/v3/private/cancel-order', 'private', 'POST', {'cost': 2.5})
    private_post_spot_v3_private_cancel_orders = privatePostSpotV3PrivateCancelOrders = Entry('spot/v3/private/cancel-orders', 'private', 'POST', {'cost': 2.5})
    private_post_spot_v3_private_cancel_orders_by_ids = privatePostSpotV3PrivateCancelOrdersByIds = Entry('spot/v3/private/cancel-orders-by-ids', 'private', 'POST', {'cost': 2.5})
    private_post_spot_v3_private_purchase = privatePostSpotV3PrivatePurchase = Entry('spot/v3/private/purchase', 'private', 'POST', {'cost': 2.5})
    private_post_spot_v3_private_redeem = privatePostSpotV3PrivateRedeem = Entry('spot/v3/private/redeem', 'private', 'POST', {'cost': 2.5})
    private_post_spot_v3_private_cross_margin_loan = privatePostSpotV3PrivateCrossMarginLoan = Entry('spot/v3/private/cross-margin-loan', 'private', 'POST', {'cost': 10})
    private_post_spot_v3_private_cross_margin_repay = privatePostSpotV3PrivateCrossMarginRepay = Entry('spot/v3/private/cross-margin-repay', 'private', 'POST', {'cost': 10})
    private_post_asset_v3_private_transfer_inter_transfer = privatePostAssetV3PrivateTransferInterTransfer = Entry('asset/v3/private/transfer/inter-transfer', 'private', 'POST', {'cost': 150})
    private_post_asset_v3_private_withdraw_create = privatePostAssetV3PrivateWithdrawCreate = Entry('asset/v3/private/withdraw/create', 'private', 'POST', {'cost': 300})
    private_post_asset_v3_private_withdraw_cancel = privatePostAssetV3PrivateWithdrawCancel = Entry('asset/v3/private/withdraw/cancel', 'private', 'POST', {'cost': 50})
    private_post_asset_v3_private_transfer_sub_member_transfer = privatePostAssetV3PrivateTransferSubMemberTransfer = Entry('asset/v3/private/transfer/sub-member-transfer', 'private', 'POST', {'cost': 150})
    private_post_asset_v3_private_transfer_transfer_sub_member_save = privatePostAssetV3PrivateTransferTransferSubMemberSave = Entry('asset/v3/private/transfer/transfer-sub-member-save', 'private', 'POST', {'cost': 150})
    private_post_asset_v3_private_transfer_universal_transfer = privatePostAssetV3PrivateTransferUniversalTransfer = Entry('asset/v3/private/transfer/universal-transfer', 'private', 'POST', {'cost': 10})
    private_post_user_v3_private_create_sub_member = privatePostUserV3PrivateCreateSubMember = Entry('user/v3/private/create-sub-member', 'private', 'POST', {'cost': 10})
    private_post_user_v3_private_create_sub_api = privatePostUserV3PrivateCreateSubApi = Entry('user/v3/private/create-sub-api', 'private', 'POST', {'cost': 10})
    private_post_user_v3_private_update_api = privatePostUserV3PrivateUpdateApi = Entry('user/v3/private/update-api', 'private', 'POST', {'cost': 10})
    private_post_user_v3_private_delete_api = privatePostUserV3PrivateDeleteApi = Entry('user/v3/private/delete-api', 'private', 'POST', {'cost': 10})
    private_post_user_v3_private_update_sub_api = privatePostUserV3PrivateUpdateSubApi = Entry('user/v3/private/update-sub-api', 'private', 'POST', {'cost': 10})
    private_post_user_v3_private_delete_sub_api = privatePostUserV3PrivateDeleteSubApi = Entry('user/v3/private/delete-sub-api', 'private', 'POST', {'cost': 10})
    private_post_contract_v3_private_copytrading_order_create = privatePostContractV3PrivateCopytradingOrderCreate = Entry('contract/v3/private/copytrading/order/create', 'private', 'POST', {'cost': 30})
    private_post_contract_v3_private_copytrading_order_cancel = privatePostContractV3PrivateCopytradingOrderCancel = Entry('contract/v3/private/copytrading/order/cancel', 'private', 'POST', {'cost': 30})
    private_post_contract_v3_private_copytrading_order_close = privatePostContractV3PrivateCopytradingOrderClose = Entry('contract/v3/private/copytrading/order/close', 'private', 'POST', {'cost': 30})
    private_post_contract_v3_private_copytrading_position_close = privatePostContractV3PrivateCopytradingPositionClose = Entry('contract/v3/private/copytrading/position/close', 'private', 'POST', {'cost': 40})
    private_post_contract_v3_private_copytrading_position_set_leverage = privatePostContractV3PrivateCopytradingPositionSetLeverage = Entry('contract/v3/private/copytrading/position/set-leverage', 'private', 'POST', {'cost': 40})
    private_post_contract_v3_private_copytrading_wallet_transfer = privatePostContractV3PrivateCopytradingWalletTransfer = Entry('contract/v3/private/copytrading/wallet/transfer', 'private', 'POST', {'cost': 25})
    private_post_contract_v3_private_copytrading_order_trading_stop = privatePostContractV3PrivateCopytradingOrderTradingStop = Entry('contract/v3/private/copytrading/order/trading-stop', 'private', 'POST', {'cost': 2.5})
    private_post_contract_v3_private_order_create = privatePostContractV3PrivateOrderCreate = Entry('contract/v3/private/order/create', 'private', 'POST', {'cost': 1})
    private_post_contract_v3_private_order_cancel = privatePostContractV3PrivateOrderCancel = Entry('contract/v3/private/order/cancel', 'private', 'POST', {'cost': 1})
    private_post_contract_v3_private_order_cancel_all = privatePostContractV3PrivateOrderCancelAll = Entry('contract/v3/private/order/cancel-all', 'private', 'POST', {'cost': 1})
    private_post_contract_v3_private_order_replace = privatePostContractV3PrivateOrderReplace = Entry('contract/v3/private/order/replace', 'private', 'POST', {'cost': 1})
    private_post_contract_v3_private_position_set_auto_add_margin = privatePostContractV3PrivatePositionSetAutoAddMargin = Entry('contract/v3/private/position/set-auto-add-margin', 'private', 'POST', {'cost': 1})
    private_post_contract_v3_private_position_switch_isolated = privatePostContractV3PrivatePositionSwitchIsolated = Entry('contract/v3/private/position/switch-isolated', 'private', 'POST', {'cost': 1})
    private_post_contract_v3_private_position_switch_mode = privatePostContractV3PrivatePositionSwitchMode = Entry('contract/v3/private/position/switch-mode', 'private', 'POST', {'cost': 1})
    private_post_contract_v3_private_position_switch_tpsl_mode = privatePostContractV3PrivatePositionSwitchTpslMode = Entry('contract/v3/private/position/switch-tpsl-mode', 'private', 'POST', {'cost': 1})
    private_post_contract_v3_private_position_set_leverage = privatePostContractV3PrivatePositionSetLeverage = Entry('contract/v3/private/position/set-leverage', 'private', 'POST', {'cost': 1})
    private_post_contract_v3_private_position_trading_stop = privatePostContractV3PrivatePositionTradingStop = Entry('contract/v3/private/position/trading-stop', 'private', 'POST', {'cost': 1})
    private_post_contract_v3_private_position_set_risk_limit = privatePostContractV3PrivatePositionSetRiskLimit = Entry('contract/v3/private/position/set-risk-limit', 'private', 'POST', {'cost': 1})
    private_post_contract_v3_private_account_setmarginmode = privatePostContractV3PrivateAccountSetMarginMode = Entry('contract/v3/private/account/setMarginMode', 'private', 'POST', {'cost': 1})
    private_post_unified_v3_private_order_create = privatePostUnifiedV3PrivateOrderCreate = Entry('unified/v3/private/order/create', 'private', 'POST', {'cost': 30})
    private_post_unified_v3_private_order_replace = privatePostUnifiedV3PrivateOrderReplace = Entry('unified/v3/private/order/replace', 'private', 'POST', {'cost': 30})
    private_post_unified_v3_private_order_cancel = privatePostUnifiedV3PrivateOrderCancel = Entry('unified/v3/private/order/cancel', 'private', 'POST', {'cost': 30})
    private_post_unified_v3_private_order_create_batch = privatePostUnifiedV3PrivateOrderCreateBatch = Entry('unified/v3/private/order/create-batch', 'private', 'POST', {'cost': 30})
    private_post_unified_v3_private_order_replace_batch = privatePostUnifiedV3PrivateOrderReplaceBatch = Entry('unified/v3/private/order/replace-batch', 'private', 'POST', {'cost': 30})
    private_post_unified_v3_private_order_cancel_batch = privatePostUnifiedV3PrivateOrderCancelBatch = Entry('unified/v3/private/order/cancel-batch', 'private', 'POST', {'cost': 30})
    private_post_unified_v3_private_order_cancel_all = privatePostUnifiedV3PrivateOrderCancelAll = Entry('unified/v3/private/order/cancel-all', 'private', 'POST', {'cost': 30})
    private_post_unified_v3_private_position_set_leverage = privatePostUnifiedV3PrivatePositionSetLeverage = Entry('unified/v3/private/position/set-leverage', 'private', 'POST', {'cost': 2.5})
    private_post_unified_v3_private_position_tpsl_switch_mode = privatePostUnifiedV3PrivatePositionTpslSwitchMode = Entry('unified/v3/private/position/tpsl/switch-mode', 'private', 'POST', {'cost': 2.5})
    private_post_unified_v3_private_position_set_risk_limit = privatePostUnifiedV3PrivatePositionSetRiskLimit = Entry('unified/v3/private/position/set-risk-limit', 'private', 'POST', {'cost': 2.5})
    private_post_unified_v3_private_position_trading_stop = privatePostUnifiedV3PrivatePositionTradingStop = Entry('unified/v3/private/position/trading-stop', 'private', 'POST', {'cost': 2.5})
    private_post_unified_v3_private_account_upgrade_unified_account = privatePostUnifiedV3PrivateAccountUpgradeUnifiedAccount = Entry('unified/v3/private/account/upgrade-unified-account', 'private', 'POST', {'cost': 2.5})
    private_post_unified_v3_private_account_setmarginmode = privatePostUnifiedV3PrivateAccountSetMarginMode = Entry('unified/v3/private/account/setMarginMode', 'private', 'POST', {'cost': 2.5})
    private_post_fht_compliance_tax_v3_private_registertime = privatePostFhtComplianceTaxV3PrivateRegistertime = Entry('fht/compliance/tax/v3/private/registertime', 'private', 'POST', {'cost': 50})
    private_post_fht_compliance_tax_v3_private_create = privatePostFhtComplianceTaxV3PrivateCreate = Entry('fht/compliance/tax/v3/private/create', 'private', 'POST', {'cost': 50})
    private_post_fht_compliance_tax_v3_private_status = privatePostFhtComplianceTaxV3PrivateStatus = Entry('fht/compliance/tax/v3/private/status', 'private', 'POST', {'cost': 50})
    private_post_fht_compliance_tax_v3_private_url = privatePostFhtComplianceTaxV3PrivateUrl = Entry('fht/compliance/tax/v3/private/url', 'private', 'POST', {'cost': 50})
    private_post_v5_order_create = privatePostV5OrderCreate = Entry('v5/order/create', 'private', 'POST', {'cost': 2.5})
    private_post_v5_order_amend = privatePostV5OrderAmend = Entry('v5/order/amend', 'private', 'POST', {'cost': 5})
    private_post_v5_order_cancel = privatePostV5OrderCancel = Entry('v5/order/cancel', 'private', 'POST', {'cost': 2.5})
    private_post_v5_order_cancel_all = privatePostV5OrderCancelAll = Entry('v5/order/cancel-all', 'private', 'POST', {'cost': 50})
    private_post_v5_order_create_batch = privatePostV5OrderCreateBatch = Entry('v5/order/create-batch', 'private', 'POST', {'cost': 5})
    private_post_v5_order_amend_batch = privatePostV5OrderAmendBatch = Entry('v5/order/amend-batch', 'private', 'POST', {'cost': 5})
    private_post_v5_order_cancel_batch = privatePostV5OrderCancelBatch = Entry('v5/order/cancel-batch', 'private', 'POST', {'cost': 5})
    private_post_v5_order_disconnected_cancel_all = privatePostV5OrderDisconnectedCancelAll = Entry('v5/order/disconnected-cancel-all', 'private', 'POST', {'cost': 5})
    private_post_v5_position_set_leverage = privatePostV5PositionSetLeverage = Entry('v5/position/set-leverage', 'private', 'POST', {'cost': 5})
    private_post_v5_position_switch_isolated = privatePostV5PositionSwitchIsolated = Entry('v5/position/switch-isolated', 'private', 'POST', {'cost': 5})
    private_post_v5_position_set_tpsl_mode = privatePostV5PositionSetTpslMode = Entry('v5/position/set-tpsl-mode', 'private', 'POST', {'cost': 5})
    private_post_v5_position_switch_mode = privatePostV5PositionSwitchMode = Entry('v5/position/switch-mode', 'private', 'POST', {'cost': 5})
    private_post_v5_position_set_risk_limit = privatePostV5PositionSetRiskLimit = Entry('v5/position/set-risk-limit', 'private', 'POST', {'cost': 5})
    private_post_v5_position_trading_stop = privatePostV5PositionTradingStop = Entry('v5/position/trading-stop', 'private', 'POST', {'cost': 5})
    private_post_v5_position_set_auto_add_margin = privatePostV5PositionSetAutoAddMargin = Entry('v5/position/set-auto-add-margin', 'private', 'POST', {'cost': 5})
    private_post_v5_position_add_margin = privatePostV5PositionAddMargin = Entry('v5/position/add-margin', 'private', 'POST', {'cost': 5})
    private_post_v5_position_move_positions = privatePostV5PositionMovePositions = Entry('v5/position/move-positions', 'private', 'POST', {'cost': 5})
    private_post_v5_position_confirm_pending_mmr = privatePostV5PositionConfirmPendingMmr = Entry('v5/position/confirm-pending-mmr', 'private', 'POST', {'cost': 5})
    private_post_v5_account_upgrade_to_uta = privatePostV5AccountUpgradeToUta = Entry('v5/account/upgrade-to-uta', 'private', 'POST', {'cost': 5})
    private_post_v5_account_quick_repayment = privatePostV5AccountQuickRepayment = Entry('v5/account/quick-repayment', 'private', 'POST', {'cost': 5})
    private_post_v5_account_set_margin_mode = privatePostV5AccountSetMarginMode = Entry('v5/account/set-margin-mode', 'private', 'POST', {'cost': 5})
    private_post_v5_account_set_hedging_mode = privatePostV5AccountSetHedgingMode = Entry('v5/account/set-hedging-mode', 'private', 'POST', {'cost': 5})
    private_post_v5_account_mmp_modify = privatePostV5AccountMmpModify = Entry('v5/account/mmp-modify', 'private', 'POST', {'cost': 5})
    private_post_v5_account_mmp_reset = privatePostV5AccountMmpReset = Entry('v5/account/mmp-reset', 'private', 'POST', {'cost': 5})
    private_post_v5_asset_transfer_inter_transfer = privatePostV5AssetTransferInterTransfer = Entry('v5/asset/transfer/inter-transfer', 'private', 'POST', {'cost': 50})
    private_post_v5_asset_transfer_save_transfer_sub_member = privatePostV5AssetTransferSaveTransferSubMember = Entry('v5/asset/transfer/save-transfer-sub-member', 'private', 'POST', {'cost': 150})
    private_post_v5_asset_transfer_universal_transfer = privatePostV5AssetTransferUniversalTransfer = Entry('v5/asset/transfer/universal-transfer', 'private', 'POST', {'cost': 10})
    private_post_v5_asset_deposit_deposit_to_account = privatePostV5AssetDepositDepositToAccount = Entry('v5/asset/deposit/deposit-to-account', 'private', 'POST', {'cost': 5})
    private_post_v5_asset_withdraw_create = privatePostV5AssetWithdrawCreate = Entry('v5/asset/withdraw/create', 'private', 'POST', {'cost': 50})
    private_post_v5_asset_withdraw_cancel = privatePostV5AssetWithdrawCancel = Entry('v5/asset/withdraw/cancel', 'private', 'POST', {'cost': 50})
    private_post_v5_user_create_sub_member = privatePostV5UserCreateSubMember = Entry('v5/user/create-sub-member', 'private', 'POST', {'cost': 10})
    private_post_v5_user_create_sub_api = privatePostV5UserCreateSubApi = Entry('v5/user/create-sub-api', 'private', 'POST', {'cost': 10})
    private_post_v5_user_frozen_sub_member = privatePostV5UserFrozenSubMember = Entry('v5/user/frozen-sub-member', 'private', 'POST', {'cost': 10})
    private_post_v5_user_update_api = privatePostV5UserUpdateApi = Entry('v5/user/update-api', 'private', 'POST', {'cost': 10})
    private_post_v5_user_update_sub_api = privatePostV5UserUpdateSubApi = Entry('v5/user/update-sub-api', 'private', 'POST', {'cost': 10})
    private_post_v5_user_delete_api = privatePostV5UserDeleteApi = Entry('v5/user/delete-api', 'private', 'POST', {'cost': 10})
    private_post_v5_user_delete_sub_api = privatePostV5UserDeleteSubApi = Entry('v5/user/delete-sub-api', 'private', 'POST', {'cost': 10})
    private_post_v5_spot_lever_token_purchase = privatePostV5SpotLeverTokenPurchase = Entry('v5/spot-lever-token/purchase', 'private', 'POST', {'cost': 2.5})
    private_post_v5_spot_lever_token_redeem = privatePostV5SpotLeverTokenRedeem = Entry('v5/spot-lever-token/redeem', 'private', 'POST', {'cost': 2.5})
    private_post_v5_spot_margin_trade_switch_mode = privatePostV5SpotMarginTradeSwitchMode = Entry('v5/spot-margin-trade/switch-mode', 'private', 'POST', {'cost': 5})
    private_post_v5_spot_margin_trade_set_leverage = privatePostV5SpotMarginTradeSetLeverage = Entry('v5/spot-margin-trade/set-leverage', 'private', 'POST', {'cost': 5})
    private_post_v5_spot_cross_margin_trade_loan = privatePostV5SpotCrossMarginTradeLoan = Entry('v5/spot-cross-margin-trade/loan', 'private', 'POST', {'cost': 2.5})
    private_post_v5_spot_cross_margin_trade_repay = privatePostV5SpotCrossMarginTradeRepay = Entry('v5/spot-cross-margin-trade/repay', 'private', 'POST', {'cost': 2.5})
    private_post_v5_spot_cross_margin_trade_switch = privatePostV5SpotCrossMarginTradeSwitch = Entry('v5/spot-cross-margin-trade/switch', 'private', 'POST', {'cost': 2.5})
    private_post_v5_ins_loan_association_uid = privatePostV5InsLoanAssociationUid = Entry('v5/ins-loan/association-uid', 'private', 'POST', {'cost': 5})
    private_post_v5_lending_purchase = privatePostV5LendingPurchase = Entry('v5/lending/purchase', 'private', 'POST', {'cost': 5})
    private_post_v5_lending_redeem = privatePostV5LendingRedeem = Entry('v5/lending/redeem', 'private', 'POST', {'cost': 5})
    private_post_v5_lending_redeem_cancel = privatePostV5LendingRedeemCancel = Entry('v5/lending/redeem-cancel', 'private', 'POST', {'cost': 5})
    private_post_v5_account_set_collateral_switch = privatePostV5AccountSetCollateralSwitch = Entry('v5/account/set-collateral-switch', 'private', 'POST', {'cost': 5})
    private_post_v5_account_set_collateral_switch_batch = privatePostV5AccountSetCollateralSwitchBatch = Entry('v5/account/set-collateral-switch-batch', 'private', 'POST', {'cost': 5})
    private_post_v5_account_demo_apply_money = privatePostV5AccountDemoApplyMoney = Entry('v5/account/demo-apply-money', 'private', 'POST', {'cost': 5})
