from ccxt.base.types import Entry


class ImplicitAPI:
    v2public_get_reference_currencies = v2PublicGetReferenceCurrencies = Entry('reference/currencies', 'v2Public', 'GET', {'cost': 1})
    v2public_get_market_status = v2PublicGetMarketStatus = Entry('market-status', 'v2Public', 'GET', {'cost': 1})
    v2private_get_account_ledger = v2PrivateGetAccountLedger = Entry('account/ledger', 'v2Private', 'GET', {'cost': 1})
    v2private_get_account_withdraw_quota = v2PrivateGetAccountWithdrawQuota = Entry('account/withdraw/quota', 'v2Private', 'GET', {'cost': 1})
    v2private_get_account_withdraw_address = v2PrivateGetAccountWithdrawAddress = Entry('account/withdraw/address', 'v2Private', 'GET', {'cost': 1})
    v2private_get_account_deposit_address = v2PrivateGetAccountDepositAddress = Entry('account/deposit/address', 'v2Private', 'GET', {'cost': 1})
    v2private_get_account_repayment = v2PrivateGetAccountRepayment = Entry('account/repayment', 'v2Private', 'GET', {'cost': 5})
    v2private_get_reference_transact_fee_rate = v2PrivateGetReferenceTransactFeeRate = Entry('reference/transact-fee-rate', 'v2Private', 'GET', {'cost': 1})
    v2private_get_account_asset_valuation = v2PrivateGetAccountAssetValuation = Entry('account/asset-valuation', 'v2Private', 'GET', {'cost': 0.2})
    v2private_get_point_account = v2PrivateGetPointAccount = Entry('point/account', 'v2Private', 'GET', {'cost': 5})
    v2private_get_sub_user_user_list = v2PrivateGetSubUserUserList = Entry('sub-user/user-list', 'v2Private', 'GET', {'cost': 1})
    v2private_get_sub_user_user_state = v2PrivateGetSubUserUserState = Entry('sub-user/user-state', 'v2Private', 'GET', {'cost': 1})
    v2private_get_sub_user_account_list = v2PrivateGetSubUserAccountList = Entry('sub-user/account-list', 'v2Private', 'GET', {'cost': 1})
    v2private_get_sub_user_deposit_address = v2PrivateGetSubUserDepositAddress = Entry('sub-user/deposit-address', 'v2Private', 'GET', {'cost': 1})
    v2private_get_sub_user_query_deposit = v2PrivateGetSubUserQueryDeposit = Entry('sub-user/query-deposit', 'v2Private', 'GET', {'cost': 1})
    v2private_get_user_api_key = v2PrivateGetUserApiKey = Entry('user/api-key', 'v2Private', 'GET', {'cost': 1})
    v2private_get_user_uid = v2PrivateGetUserUid = Entry('user/uid', 'v2Private', 'GET', {'cost': 1})
    v2private_get_algo_orders_opening = v2PrivateGetAlgoOrdersOpening = Entry('algo-orders/opening', 'v2Private', 'GET', {'cost': 1})
    v2private_get_algo_orders_history = v2PrivateGetAlgoOrdersHistory = Entry('algo-orders/history', 'v2Private', 'GET', {'cost': 1})
    v2private_get_algo_orders_specific = v2PrivateGetAlgoOrdersSpecific = Entry('algo-orders/specific', 'v2Private', 'GET', {'cost': 1})
    v2private_get_c2c_offers = v2PrivateGetC2cOffers = Entry('c2c/offers', 'v2Private', 'GET', {'cost': 1})
    v2private_get_c2c_offer = v2PrivateGetC2cOffer = Entry('c2c/offer', 'v2Private', 'GET', {'cost': 1})
    v2private_get_c2c_transactions = v2PrivateGetC2cTransactions = Entry('c2c/transactions', 'v2Private', 'GET', {'cost': 1})
    v2private_get_c2c_repayment = v2PrivateGetC2cRepayment = Entry('c2c/repayment', 'v2Private', 'GET', {'cost': 1})
    v2private_get_c2c_account = v2PrivateGetC2cAccount = Entry('c2c/account', 'v2Private', 'GET', {'cost': 1})
    v2private_get_etp_reference = v2PrivateGetEtpReference = Entry('etp/reference', 'v2Private', 'GET', {'cost': 1})
    v2private_get_etp_transactions = v2PrivateGetEtpTransactions = Entry('etp/transactions', 'v2Private', 'GET', {'cost': 5})
    v2private_get_etp_transaction = v2PrivateGetEtpTransaction = Entry('etp/transaction', 'v2Private', 'GET', {'cost': 5})
    v2private_get_etp_rebalance = v2PrivateGetEtpRebalance = Entry('etp/rebalance', 'v2Private', 'GET', {'cost': 1})
    v2private_get_etp_limit = v2PrivateGetEtpLimit = Entry('etp/limit', 'v2Private', 'GET', {'cost': 1})
    v2private_post_account_transfer = v2PrivatePostAccountTransfer = Entry('account/transfer', 'v2Private', 'POST', {'cost': 1})
    v2private_post_account_repayment = v2PrivatePostAccountRepayment = Entry('account/repayment', 'v2Private', 'POST', {'cost': 5})
    v2private_post_point_transfer = v2PrivatePostPointTransfer = Entry('point/transfer', 'v2Private', 'POST', {'cost': 5})
    v2private_post_sub_user_management = v2PrivatePostSubUserManagement = Entry('sub-user/management', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_creation = v2PrivatePostSubUserCreation = Entry('sub-user/creation', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_tradable_market = v2PrivatePostSubUserTradableMarket = Entry('sub-user/tradable-market', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_transferability = v2PrivatePostSubUserTransferability = Entry('sub-user/transferability', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_api_key_generation = v2PrivatePostSubUserApiKeyGeneration = Entry('sub-user/api-key-generation', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_api_key_modification = v2PrivatePostSubUserApiKeyModification = Entry('sub-user/api-key-modification', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_api_key_deletion = v2PrivatePostSubUserApiKeyDeletion = Entry('sub-user/api-key-deletion', 'v2Private', 'POST', {'cost': 1})
    v2private_post_sub_user_deduct_mode = v2PrivatePostSubUserDeductMode = Entry('sub-user/deduct-mode', 'v2Private', 'POST', {'cost': 1})
    v2private_post_algo_orders = v2PrivatePostAlgoOrders = Entry('algo-orders', 'v2Private', 'POST', {'cost': 1})
    v2private_post_algo_orders_cancel_all_after = v2PrivatePostAlgoOrdersCancelAllAfter = Entry('algo-orders/cancel-all-after', 'v2Private', 'POST', {'cost': 1})
    v2private_post_algo_orders_cancellation = v2PrivatePostAlgoOrdersCancellation = Entry('algo-orders/cancellation', 'v2Private', 'POST', {'cost': 1})
    v2private_post_c2c_offer = v2PrivatePostC2cOffer = Entry('c2c/offer', 'v2Private', 'POST', {'cost': 1})
    v2private_post_c2c_cancellation = v2PrivatePostC2cCancellation = Entry('c2c/cancellation', 'v2Private', 'POST', {'cost': 1})
    v2private_post_c2c_cancel_all = v2PrivatePostC2cCancelAll = Entry('c2c/cancel-all', 'v2Private', 'POST', {'cost': 1})
    v2private_post_c2c_repayment = v2PrivatePostC2cRepayment = Entry('c2c/repayment', 'v2Private', 'POST', {'cost': 1})
    v2private_post_c2c_transfer = v2PrivatePostC2cTransfer = Entry('c2c/transfer', 'v2Private', 'POST', {'cost': 1})
    v2private_post_etp_creation = v2PrivatePostEtpCreation = Entry('etp/creation', 'v2Private', 'POST', {'cost': 5})
    v2private_post_etp_redemption = v2PrivatePostEtpRedemption = Entry('etp/redemption', 'v2Private', 'POST', {'cost': 5})
    v2private_post_etp_transactid_cancel = v2PrivatePostEtpTransactIdCancel = Entry('etp/{transactId}/cancel', 'v2Private', 'POST', {'cost': 10})
    v2private_post_etp_batch_cancel = v2PrivatePostEtpBatchCancel = Entry('etp/batch-cancel', 'v2Private', 'POST', {'cost': 50})
    market_get_history_kline = marketGetHistoryKline = Entry('history/kline', 'market', 'GET', {'cost': 1})
    market_get_detail_merged = marketGetDetailMerged = Entry('detail/merged', 'market', 'GET', {'cost': 1})
    market_get_depth = marketGetDepth = Entry('depth', 'market', 'GET', {'cost': 1})
    market_get_trade = marketGetTrade = Entry('trade', 'market', 'GET', {'cost': 1})
    market_get_history_trade = marketGetHistoryTrade = Entry('history/trade', 'market', 'GET', {'cost': 1})
    market_get_detail = marketGetDetail = Entry('detail', 'market', 'GET', {'cost': 1})
    market_get_tickers = marketGetTickers = Entry('tickers', 'market', 'GET', {'cost': 1})
    market_get_etp = marketGetEtp = Entry('etp', 'market', 'GET', {'cost': 1})
    public_get_common_symbols = publicGetCommonSymbols = Entry('common/symbols', 'public', 'GET', {'cost': 1})
    public_get_common_currencys = publicGetCommonCurrencys = Entry('common/currencys', 'public', 'GET', {'cost': 1})
    public_get_common_timestamp = publicGetCommonTimestamp = Entry('common/timestamp', 'public', 'GET', {'cost': 1})
    public_get_common_exchange = publicGetCommonExchange = Entry('common/exchange', 'public', 'GET', {'cost': 1})
    public_get_settings_currencys = publicGetSettingsCurrencys = Entry('settings/currencys', 'public', 'GET', {'cost': 1})
    private_get_account_accounts = privateGetAccountAccounts = Entry('account/accounts', 'private', 'GET', {'cost': 0.2})
    private_get_account_accounts_id_balance = privateGetAccountAccountsIdBalance = Entry('account/accounts/{id}/balance', 'private', 'GET', {'cost': 0.2})
    private_get_account_accounts_sub_uid = privateGetAccountAccountsSubUid = Entry('account/accounts/{sub-uid}', 'private', 'GET', {'cost': 1})
    private_get_account_history = privateGetAccountHistory = Entry('account/history', 'private', 'GET', {'cost': 4})
    private_get_cross_margin_loan_info = privateGetCrossMarginLoanInfo = Entry('cross-margin/loan-info', 'private', 'GET', {'cost': 1})
    private_get_margin_loan_info = privateGetMarginLoanInfo = Entry('margin/loan-info', 'private', 'GET', {'cost': 1})
    private_get_fee_fee_rate_get = privateGetFeeFeeRateGet = Entry('fee/fee-rate/get', 'private', 'GET', {'cost': 1})
    private_get_order_openorders = privateGetOrderOpenOrders = Entry('order/openOrders', 'private', 'GET', {'cost': 0.4})
    private_get_order_orders = privateGetOrderOrders = Entry('order/orders', 'private', 'GET', {'cost': 0.4})
    private_get_order_orders_id = privateGetOrderOrdersId = Entry('order/orders/{id}', 'private', 'GET', {'cost': 0.4})
    private_get_order_orders_id_matchresults = privateGetOrderOrdersIdMatchresults = Entry('order/orders/{id}/matchresults', 'private', 'GET', {'cost': 0.4})
    private_get_order_orders_getclientorder = privateGetOrderOrdersGetClientOrder = Entry('order/orders/getClientOrder', 'private', 'GET', {'cost': 0.4})
    private_get_order_history = privateGetOrderHistory = Entry('order/history', 'private', 'GET', {'cost': 1})
    private_get_order_matchresults = privateGetOrderMatchresults = Entry('order/matchresults', 'private', 'GET', {'cost': 1})
    private_get_query_deposit_withdraw = privateGetQueryDepositWithdraw = Entry('query/deposit-withdraw', 'private', 'GET', {'cost': 1})
    private_get_margin_loan_orders = privateGetMarginLoanOrders = Entry('margin/loan-orders', 'private', 'GET', {'cost': 0.2})
    private_get_margin_accounts_balance = privateGetMarginAccountsBalance = Entry('margin/accounts/balance', 'private', 'GET', {'cost': 0.2})
    private_get_cross_margin_loan_orders = privateGetCrossMarginLoanOrders = Entry('cross-margin/loan-orders', 'private', 'GET', {'cost': 1})
    private_get_cross_margin_accounts_balance = privateGetCrossMarginAccountsBalance = Entry('cross-margin/accounts/balance', 'private', 'GET', {'cost': 1})
    private_get_points_actions = privateGetPointsActions = Entry('points/actions', 'private', 'GET', {'cost': 1})
    private_get_points_orders = privateGetPointsOrders = Entry('points/orders', 'private', 'GET', {'cost': 1})
    private_get_subuser_aggregate_balance = privateGetSubuserAggregateBalance = Entry('subuser/aggregate-balance', 'private', 'GET', {'cost': 10})
    private_get_stable_coin_exchange_rate = privateGetStableCoinExchangeRate = Entry('stable-coin/exchange_rate', 'private', 'GET', {'cost': 1})
    private_get_stable_coin_quote = privateGetStableCoinQuote = Entry('stable-coin/quote', 'private', 'GET', {'cost': 1})
    private_post_account_transfer = privatePostAccountTransfer = Entry('account/transfer', 'private', 'POST', {'cost': 1})
    private_post_futures_transfer = privatePostFuturesTransfer = Entry('futures/transfer', 'private', 'POST', {'cost': 1})
    private_post_order_batch_orders = privatePostOrderBatchOrders = Entry('order/batch-orders', 'private', 'POST', {'cost': 0.4})
    private_post_order_orders_place = privatePostOrderOrdersPlace = Entry('order/orders/place', 'private', 'POST', {'cost': 0.2})
    private_post_order_orders_submitcancelclientorder = privatePostOrderOrdersSubmitCancelClientOrder = Entry('order/orders/submitCancelClientOrder', 'private', 'POST', {'cost': 0.2})
    private_post_order_orders_batchcancelopenorders = privatePostOrderOrdersBatchCancelOpenOrders = Entry('order/orders/batchCancelOpenOrders', 'private', 'POST', {'cost': 0.4})
    private_post_order_orders_id_submitcancel = privatePostOrderOrdersIdSubmitcancel = Entry('order/orders/{id}/submitcancel', 'private', 'POST', {'cost': 0.2})
    private_post_order_orders_batchcancel = privatePostOrderOrdersBatchcancel = Entry('order/orders/batchcancel', 'private', 'POST', {'cost': 0.4})
    private_post_dw_withdraw_api_create = privatePostDwWithdrawApiCreate = Entry('dw/withdraw/api/create', 'private', 'POST', {'cost': 1})
    private_post_dw_withdraw_virtual_id_cancel = privatePostDwWithdrawVirtualIdCancel = Entry('dw/withdraw-virtual/{id}/cancel', 'private', 'POST', {'cost': 1})
    private_post_dw_transfer_in_margin = privatePostDwTransferInMargin = Entry('dw/transfer-in/margin', 'private', 'POST', {'cost': 10})
    private_post_dw_transfer_out_margin = privatePostDwTransferOutMargin = Entry('dw/transfer-out/margin', 'private', 'POST', {'cost': 10})
    private_post_margin_orders = privatePostMarginOrders = Entry('margin/orders', 'private', 'POST', {'cost': 10})
    private_post_margin_orders_id_repay = privatePostMarginOrdersIdRepay = Entry('margin/orders/{id}/repay', 'private', 'POST', {'cost': 10})
    private_post_cross_margin_transfer_in = privatePostCrossMarginTransferIn = Entry('cross-margin/transfer-in', 'private', 'POST', {'cost': 1})
    private_post_cross_margin_transfer_out = privatePostCrossMarginTransferOut = Entry('cross-margin/transfer-out', 'private', 'POST', {'cost': 1})
    private_post_cross_margin_orders = privatePostCrossMarginOrders = Entry('cross-margin/orders', 'private', 'POST', {'cost': 1})
    private_post_cross_margin_orders_id_repay = privatePostCrossMarginOrdersIdRepay = Entry('cross-margin/orders/{id}/repay', 'private', 'POST', {'cost': 1})
    private_post_stable_coin_exchange = privatePostStableCoinExchange = Entry('stable-coin/exchange', 'private', 'POST', {'cost': 1})
    private_post_subuser_transfer = privatePostSubuserTransfer = Entry('subuser/transfer', 'private', 'POST', {'cost': 10})
