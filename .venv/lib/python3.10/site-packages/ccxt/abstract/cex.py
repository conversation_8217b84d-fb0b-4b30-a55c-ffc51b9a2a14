from ccxt.base.types import Entry


class ImplicitAPI:
    public_get_currency_profile = publicGetCurrencyProfile = Entry('currency_profile', 'public', 'GET', {})
    public_get_currency_limits = publicGetCurrencyLimits = Entry('currency_limits/', 'public', 'GET', {})
    public_get_last_price_pair = publicGetLastPricePair = Entry('last_price/{pair}/', 'public', 'GET', {})
    public_get_last_prices_currencies = publicGetLastPricesCurrencies = Entry('last_prices/{currencies}/', 'public', 'GET', {})
    public_get_ohlcv_hd_yyyymmdd_pair = publicGetOhlcvHdYyyymmddPair = Entry('ohlcv/hd/{yyyymmdd}/{pair}', 'public', 'GET', {})
    public_get_order_book_pair = publicGetOrderBookPair = Entry('order_book/{pair}/', 'public', 'GET', {})
    public_get_ticker_pair = publicGetTickerPair = Entry('ticker/{pair}/', 'public', 'GET', {})
    public_get_tickers_currencies = publicGetTickersCurrencies = Entry('tickers/{currencies}/', 'public', 'GET', {})
    public_get_trade_history_pair = publicGetTradeHistoryPair = Entry('trade_history/{pair}/', 'public', 'GET', {})
    public_post_convert_pair = publicPostConvertPair = Entry('convert/{pair}', 'public', 'POST', {})
    public_post_price_stats_pair = publicPostPriceStatsPair = Entry('price_stats/{pair}', 'public', 'POST', {})
    private_post_active_orders_status = privatePostActiveOrdersStatus = Entry('active_orders_status/', 'private', 'POST', {})
    private_post_archived_orders_pair = privatePostArchivedOrdersPair = Entry('archived_orders/{pair}/', 'private', 'POST', {})
    private_post_balance = privatePostBalance = Entry('balance/', 'private', 'POST', {})
    private_post_cancel_order = privatePostCancelOrder = Entry('cancel_order/', 'private', 'POST', {})
    private_post_cancel_orders_pair = privatePostCancelOrdersPair = Entry('cancel_orders/{pair}/', 'private', 'POST', {})
    private_post_cancel_replace_order_pair = privatePostCancelReplaceOrderPair = Entry('cancel_replace_order/{pair}/', 'private', 'POST', {})
    private_post_close_position_pair = privatePostClosePositionPair = Entry('close_position/{pair}/', 'private', 'POST', {})
    private_post_get_address = privatePostGetAddress = Entry('get_address/', 'private', 'POST', {})
    private_post_get_crypto_address = privatePostGetCryptoAddress = Entry('get_crypto_address', 'private', 'POST', {})
    private_post_get_myfee = privatePostGetMyfee = Entry('get_myfee/', 'private', 'POST', {})
    private_post_get_order = privatePostGetOrder = Entry('get_order/', 'private', 'POST', {})
    private_post_get_order_tx = privatePostGetOrderTx = Entry('get_order_tx/', 'private', 'POST', {})
    private_post_open_orders_pair = privatePostOpenOrdersPair = Entry('open_orders/{pair}/', 'private', 'POST', {})
    private_post_open_orders = privatePostOpenOrders = Entry('open_orders/', 'private', 'POST', {})
    private_post_open_position_pair = privatePostOpenPositionPair = Entry('open_position/{pair}/', 'private', 'POST', {})
    private_post_open_positions_pair = privatePostOpenPositionsPair = Entry('open_positions/{pair}/', 'private', 'POST', {})
    private_post_place_order_pair = privatePostPlaceOrderPair = Entry('place_order/{pair}/', 'private', 'POST', {})
    private_post_raw_tx_history = privatePostRawTxHistory = Entry('raw_tx_history', 'private', 'POST', {})
