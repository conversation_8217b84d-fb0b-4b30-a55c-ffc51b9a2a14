# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

import ccxt.async_support
from ccxt.async_support.base.ws.cache import ArrayCache, ArrayCacheBySymbolById, ArrayCacheByTimestamp
import hashlib
from ccxt.base.types import Balances, Int, Order, OrderBook, Str, Ticker, Trade
from ccxt.async_support.base.ws.client import Client
from typing import List
from ccxt.base.errors import AuthenticationError


class mexc(ccxt.async_support.mexc):

    def describe(self):
        return self.deep_extend(super(mexc, self).describe(), {
            'has': {
                'ws': True,
                'cancelAllOrdersWs': False,
                'cancelOrdersWs': False,
                'cancelOrderWs': False,
                'createOrderWs': False,
                'editOrderWs': False,
                'fetchBalanceWs': False,
                'fetchOpenOrdersWs': False,
                'fetchOrderWs': False,
                'fetchTradesWs': False,
                'watchBalance': True,
                'watchMyTrades': True,
                'watchOHLCV': True,
                'watchOrderBook': True,
                'watchOrders': True,
                'watchTicker': True,
                'watchTickers': False,
                'watchTrades': True,
            },
            'urls': {
                'api': {
                    'ws': {
                        'spot': 'wss://wbs.mexc.com/ws',
                        'swap': 'wss://contract.mexc.com/edge',
                    },
                },
            },
            'options': {
                'listenKeyRefreshRate': 1200000,
                # TODO add reset connection after  #16754 is merged
                'timeframes': {
                    '1m': 'Min1',
                    '5m': 'Min5',
                    '15m': 'Min15',
                    '30m': 'Min30',
                    '1h': 'Min60',
                    '4h': 'Hour4',
                    '8h': 'Hour8',
                    '1d': 'Day1',
                    '1w': 'Week1',
                    '1M': 'Month1',
                },
                'watchOrderBook': {
                    'snapshotDelay': 25,
                    'snapshotMaxRetries': 3,
                },
                'listenKey': None,
            },
            'streaming': {
                'ping': self.ping,
                'keepAlive': 8000,
            },
            'exceptions': {
            },
        })

    async def watch_ticker(self, symbol: str, params={}) -> Ticker:
        """
        watches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
        :param str symbol: unified symbol of the market to fetch the ticker for
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `ticker structure <https://docs.ccxt.com/#/?id=ticker-structure>`
        """
        await self.load_markets()
        market = self.market(symbol)
        messageHash = 'ticker:' + market['symbol']
        if market['spot']:
            channel = '<EMAIL>@' + market['id']
            return await self.watch_spot_public(channel, messageHash, params)
        else:
            channel = 'sub.ticker'
            requestParams: dict = {
                'symbol': market['id'],
            }
            return await self.watch_swap_public(channel, messageHash, requestParams, params)

    def handle_ticker(self, client: Client, message):
        #
        #    {
        #        "c": "<EMAIL>@BTCUSDT",
        #        "d": {
        #            "A": "4.70432",
        #            "B": "6.714863",
        #            "a": "20744.54",
        #            "b": "20744.17"
        #        },
        #        "s": "BTCUSDT",
        #        "t": 1678643605721
        #    }
        #
        rawTicker = self.safe_value_2(message, 'd', 'data')
        marketId = self.safe_string_2(message, 's', 'symbol')
        timestamp = self.safe_integer(message, 't')
        market = self.safe_market(marketId)
        symbol = market['symbol']
        ticker = None
        if market['spot']:
            ticker = self.parse_ws_ticker(rawTicker, market)
            ticker['timestamp'] = timestamp
            ticker['datetime'] = self.iso8601(timestamp)
        else:
            ticker = self.parse_ticker(rawTicker, market)
        self.tickers[symbol] = ticker
        messageHash = 'ticker:' + symbol
        client.resolve(ticker, messageHash)

    def parse_ws_ticker(self, ticker, market=None):
        #
        # spot
        #    {
        #        "A": "4.70432",
        #        "B": "6.714863",
        #        "a": "20744.54",
        #        "b": "20744.17"
        #    }
        #
        return self.safe_ticker({
            'symbol': self.safe_symbol(None, market),
            'timestamp': None,
            'datetime': None,
            'open': None,
            'high': None,
            'low': None,
            'close': None,
            'bid': self.safe_number(ticker, 'b'),
            'bidVolume': self.safe_number(ticker, 'B'),
            'ask': self.safe_number(ticker, 'a'),
            'askVolume': self.safe_number(ticker, 'A'),
            'vwap': None,
            'previousClose': None,
            'change': None,
            'percentage': None,
            'average': None,
            'baseVolume': None,
            'quoteVolume': None,
            'info': ticker,
        }, market)

    async def watch_spot_public(self, channel, messageHash, params={}):
        url = self.urls['api']['ws']['spot']
        request: dict = {
            'method': 'SUBSCRIPTION',
            'params': [channel],
        }
        return await self.watch(url, messageHash, self.extend(request, params), channel)

    async def watch_spot_private(self, channel, messageHash, params={}):
        self.check_required_credentials()
        listenKey = await self.authenticate(channel)
        url = self.urls['api']['ws']['spot'] + '?listenKey=' + listenKey
        request: dict = {
            'method': 'SUBSCRIPTION',
            'params': [channel],
        }
        return await self.watch(url, messageHash, self.extend(request, params), channel)

    async def watch_swap_public(self, channel, messageHash, requestParams, params={}):
        url = self.urls['api']['ws']['swap']
        request: dict = {
            'method': channel,
            'param': requestParams,
        }
        message = self.extend(request, params)
        return await self.watch(url, messageHash, message, messageHash)

    async def watch_swap_private(self, messageHash, params={}):
        self.check_required_credentials()
        channel = 'login'
        url = self.urls['api']['ws']['swap']
        timestamp = str(self.milliseconds())
        payload = self.apiKey + timestamp
        signature = self.hmac(self.encode(payload), self.encode(self.secret), hashlib.sha256)
        request: dict = {
            'method': channel,
            'param': {
                'apiKey': self.apiKey,
                'signature': signature,
                'reqTime': timestamp,
            },
        }
        message = self.extend(request, params)
        return await self.watch(url, messageHash, message, channel)

    async def watch_ohlcv(self, symbol: str, timeframe='1m', since: Int = None, limit: Int = None, params={}) -> List[list]:
        """
        :see: https://mxcdevelop.github.io/apidocs/spot_v3_en/#kline-streams
        watches historical candlestick data containing the open, high, low, and close price, and the volume of a market
        :param str symbol: unified symbol of the market to fetch OHLCV data for
        :param str timeframe: the length of time each candle represents
        :param int [since]: timestamp in ms of the earliest candle to fetch
        :param int [limit]: the maximum amount of candles to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns int[][]: A list of candles ordered, open, high, low, close, volume
        """
        await self.load_markets()
        market = self.market(symbol)
        symbol = market['symbol']
        timeframes = self.safe_value(self.options, 'timeframes', {})
        timeframeId = self.safe_string(timeframes, timeframe)
        messageHash = 'candles:' + symbol + ':' + timeframe
        ohlcv = None
        if market['spot']:
            channel = '<EMAIL>@' + market['id'] + '@' + timeframeId
            ohlcv = await self.watch_spot_public(channel, messageHash, params)
        else:
            channel = 'sub.kline'
            requestParams: dict = {
                'symbol': market['id'],
                'interval': timeframeId,
            }
            ohlcv = await self.watch_swap_public(channel, messageHash, requestParams, params)
        if self.newUpdates:
            limit = ohlcv.getLimit(symbol, limit)
        return self.filter_by_since_limit(ohlcv, since, limit, 0, True)

    def handle_ohlcv(self, client: Client, message):
        #
        # spot
        #
        #    {
        #        "d": {
        #            "e": "<EMAIL>",
        #            "k": {
        #                "t": 1678642261,
        #                "o": 20626.94,
        #                "c": 20599.69,
        #                "h": 20626.94,
        #                "l": 20597.06,
        #                "v": 27.678686,
        #                "a": 570332.77,
        #                "T": 1678642320,
        #                "i": "Min1"
        #            }
        #        },
        #        "c": "<EMAIL>@BTCUSDT@Min1",
        #        "t": 1678642276459,
        #        "s": "BTCUSDT"
        #    }
        #
        # swap
        #
        #   {
        #       "channel": "push.kline",
        #       "data": {
        #         "a": 325653.3287,
        #         "c": 38839,
        #         "h": 38909.5,
        #         "interval": "Min1",
        #         "l": 38833,
        #         "o": 38901.5,
        #         "q": 83808,
        #         "rc": 38839,
        #         "rh": 38909.5,
        #         "rl": 38833,
        #         "ro": 38909.5,
        #         "symbol": "BTC_USDT",
        #         "t": 1651230660
        #       },
        #       "symbol": "BTC_USDT",
        #       "ts": 1651230713067
        #   }
        #
        d = self.safe_value_2(message, 'd', 'data', {})
        rawOhlcv = self.safe_value(d, 'k', d)
        timeframeId = self.safe_string_2(rawOhlcv, 'i', 'interval')
        timeframes = self.safe_value(self.options, 'timeframes', {})
        timeframe = self.find_timeframe(timeframeId, timeframes)
        marketId = self.safe_string_2(message, 's', 'symbol')
        market = self.safe_market(marketId)
        symbol = market['symbol']
        messageHash = 'candles:' + symbol + ':' + timeframe
        parsed = self.parse_ws_ohlcv(rawOhlcv, market)
        self.ohlcvs[symbol] = self.safe_value(self.ohlcvs, symbol, {})
        stored = self.safe_value(self.ohlcvs[symbol], timeframe)
        if stored is None:
            limit = self.safe_integer(self.options, 'OHLCVLimit', 1000)
            stored = ArrayCacheByTimestamp(limit)
            self.ohlcvs[symbol][timeframe] = stored
        stored.append(parsed)
        client.resolve(stored, messageHash)

    def parse_ws_ohlcv(self, ohlcv, market=None) -> list:
        #
        # spot
        #
        #    {
        #        "t": 1678642260,
        #        "o": 20626.94,
        #        "c": 20599.69,
        #        "h": 20626.94,
        #        "l": 20597.06,
        #        "v": 27.678686,
        #        "a": 570332.77,
        #        "T": 1678642320,
        #        "i": "Min1"
        #    }
        #
        # swap
        #    {
        #       "symbol": "BTC_USDT",
        #       "interval": "Min1",
        #       "t": 1680055080,
        #       "o": 27301.9,
        #       "c": 27301.8,
        #       "h": 27301.9,
        #       "l": 27301.8,
        #       "a": 8.19054,
        #       "q": 3,
        #       "ro": 27301.8,
        #       "rc": 27301.8,
        #       "rh": 27301.8,
        #       "rl": 27301.8
        #     }
        #
        return [
            self.safe_timestamp(ohlcv, 't'),
            self.safe_number(ohlcv, 'o'),
            self.safe_number(ohlcv, 'h'),
            self.safe_number(ohlcv, 'l'),
            self.safe_number(ohlcv, 'c'),
            self.safe_number_2(ohlcv, 'v', 'q'),
        ]

    async def watch_order_book(self, symbol: str, limit: Int = None, params={}) -> OrderBook:
        """
        :see: https://mxcdevelop.github.io/apidocs/spot_v3_en/#diff-depth-stream
        watches information on open orders with bid(buy) and ask(sell) prices, volumes and other data
        :param str symbol: unified symbol of the market to fetch the order book for
        :param int [limit]: the maximum amount of order book entries to return
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: A dictionary of `order book structures <https://docs.ccxt.com/#/?id=order-book-structure>` indexed by market symbols
        """
        await self.load_markets()
        market = self.market(symbol)
        symbol = market['symbol']
        messageHash = 'orderbook:' + symbol
        orderbook = None
        if market['spot']:
            channel = '<EMAIL>@' + market['id']
            orderbook = await self.watch_spot_public(channel, messageHash, params)
        else:
            channel = 'sub.depth'
            requestParams: dict = {
                'symbol': market['id'],
            }
            orderbook = await self.watch_swap_public(channel, messageHash, requestParams, params)
        return orderbook.limit()

    def handle_order_book_subscription(self, client: Client, message):
        # spot
        #     {id: 0, code: 0, msg: "<EMAIL>@BTCUSDT"}
        #
        msg = self.safe_string(message, 'msg')
        parts = msg.split('@')
        marketId = self.safe_string(parts, 2)
        symbol = self.safe_symbol(marketId)
        self.orderbooks[symbol] = self.order_book({})

    def get_cache_index(self, orderbook, cache):
        # return the first index of the cache that can be applied to the orderbook or -1 if not possible
        nonce = self.safe_integer(orderbook, 'nonce')
        firstDelta = self.safe_value(cache, 0)
        firstDeltaNonce = self.safe_integer_2(firstDelta, 'r', 'version')
        if nonce < firstDeltaNonce - 1:
            return -1
        for i in range(0, len(cache)):
            delta = cache[i]
            deltaNonce = self.safe_integer_2(delta, 'r', 'version')
            if deltaNonce >= nonce:
                return i
        return len(cache)

    def handle_order_book(self, client: Client, message):
        #
        # spot
        #    {
        #        "c": "<EMAIL>@BTCUSDT",
        #        "d": {
        #            "asks": [{
        #                "p": "20290.89",
        #                "v": "0.000000"
        #            }],
        #            "e": "<EMAIL>",
        #            "r": "3407459756"
        #        },
        #        "s": "BTCUSDT",
        #        "t": 1661932660144
        #    }
        #
        #
        #
        # swap
        #  {
        #      "channel":"push.depth",
        #      "data":{
        #         "asks":[
        #            [
        #               39146.5,
        #               11264,
        #               1
        #            ]
        #         ],
        #         "bids":[
        #            [
        #               39144,
        #               35460,
        #               1
        #            ]
        #         ],
        #         "end":4895965272,
        #         "begin":4895965271
        #      },
        #      "symbol":"BTC_USDT",
        #      "ts":1651239652372
        #  }
        #
        data = self.safe_value_2(message, 'd', 'data')
        marketId = self.safe_string_2(message, 's', 'symbol')
        symbol = self.safe_symbol(marketId)
        messageHash = 'orderbook:' + symbol
        subscription = self.safe_value(client.subscriptions, messageHash)
        limit = self.safe_integer(subscription, 'limit')
        if subscription is True:
            # we set client.subscriptions[messageHash] to 1
            # once we have received the first delta and initialized the orderbook
            client.subscriptions[messageHash] = 1
            self.orderbooks[symbol] = self.counted_order_book({})
        storedOrderBook = self.orderbooks[symbol]
        nonce = self.safe_integer(storedOrderBook, 'nonce')
        if nonce is None:
            cacheLength = len(storedOrderBook.cache)
            snapshotDelay = self.handle_option('watchOrderBook', 'snapshotDelay', 25)
            if cacheLength == snapshotDelay:
                self.spawn(self.load_order_book, client, messageHash, symbol, limit, {})
            storedOrderBook.cache.append(data)
            return
        try:
            self.handle_delta(storedOrderBook, data)
            timestamp = self.safe_integer_2(message, 't', 'ts')
            storedOrderBook['timestamp'] = timestamp
            storedOrderBook['datetime'] = self.iso8601(timestamp)
        except Exception as e:
            del client.subscriptions[messageHash]
            client.reject(e, messageHash)
        client.resolve(storedOrderBook, messageHash)

    def handle_bookside_delta(self, bookside, bidasks):
        #
        #    [{
        #        "p": "20290.89",
        #        "v": "0.000000"
        #    }]
        #
        for i in range(0, len(bidasks)):
            bidask = bidasks[i]
            if isinstance(bidask, list):
                bookside.storeArray(bidask)
            else:
                price = self.safe_float(bidask, 'p')
                amount = self.safe_float(bidask, 'v')
                bookside.store(price, amount)

    def handle_delta(self, orderbook, delta):
        existingNonce = self.safe_integer(orderbook, 'nonce')
        deltaNonce = self.safe_integer_2(delta, 'r', 'version')
        if deltaNonce < existingNonce:
            # even when doing < comparison, self happens: https://app.travis-ci.com/github/ccxt/ccxt/builds/269234741#L1809
            # so, we just skip old updates
            return
        orderbook['nonce'] = deltaNonce
        asks = self.safe_list(delta, 'asks', [])
        bids = self.safe_list(delta, 'bids', [])
        asksOrderSide = orderbook['asks']
        bidsOrderSide = orderbook['bids']
        self.handle_bookside_delta(asksOrderSide, asks)
        self.handle_bookside_delta(bidsOrderSide, bids)

    async def watch_trades(self, symbol: str, since: Int = None, limit: Int = None, params={}) -> List[Trade]:
        """
        :see: https://mxcdevelop.github.io/apidocs/spot_v3_en/#trade-streams
        get the list of most recent trades for a particular symbol
        :param str symbol: unified symbol of the market to fetch trades for
        :param int [since]: timestamp in ms of the earliest trade to fetch
        :param int [limit]: the maximum amount of trades to fetch
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `trade structures <https://docs.ccxt.com/#/?id=public-trades>`
        """
        await self.load_markets()
        market = self.market(symbol)
        symbol = market['symbol']
        messageHash = 'trades:' + symbol
        trades = None
        if market['spot']:
            channel = '<EMAIL>@' + market['id']
            trades = await self.watch_spot_public(channel, messageHash, params)
        else:
            channel = 'sub.deal'
            requestParams: dict = {
                'symbol': market['id'],
            }
            trades = await self.watch_swap_public(channel, messageHash, requestParams, params)
        if self.newUpdates:
            limit = trades.getLimit(symbol, limit)
        return self.filter_by_since_limit(trades, since, limit, 'timestamp', True)

    def handle_trades(self, client: Client, message):
        #
        #    {
        #        "c": "<EMAIL>@BTCUSDT",
        #        "d": {
        #            "deals": [{
        #                "p": "20382.70",
        #                "v": "0.043800",
        #                "S": 1,
        #                "t": 1678593222456,
        #            },],
        #            "e": "<EMAIL>",
        #        },
        #        "s": "BTCUSDT",
        #        "t": 1678593222460,
        #    }
        #
        # swap
        #     {
        #         "symbol": "BTC_USDT",
        #         "data": {
        #             "p": 27307.3,
        #             "v": 5,
        #             "T": 2,
        #             "O": 3,
        #             "M": 1,
        #             "t": 1680055941870
        #         },
        #         "channel": "push.deal",
        #         "ts": 1680055941870
        #     }
        #
        marketId = self.safe_string_2(message, 's', 'symbol')
        market = self.safe_market(marketId)
        symbol = market['symbol']
        messageHash = 'trades:' + symbol
        stored = self.safe_value(self.trades, symbol)
        if stored is None:
            limit = self.safe_integer(self.options, 'tradesLimit', 1000)
            stored = ArrayCache(limit)
            self.trades[symbol] = stored
        d = self.safe_value_2(message, 'd', 'data')
        trades = self.safe_value(d, 'deals', [d])
        for j in range(0, len(trades)):
            parsedTrade = None
            if market['spot']:
                parsedTrade = self.parse_ws_trade(trades[j], market)
            else:
                parsedTrade = self.parse_trade(trades[j], market)
            stored.append(parsedTrade)
        client.resolve(stored, messageHash)

    async def watch_my_trades(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Trade]:
        """
        :see: https://mxcdevelop.github.io/apidocs/spot_v3_en/#spot-account-deals
        watches information on multiple trades made by the user
        :param str symbol: unified market symbol of the market trades were made in
        :param int [since]: the earliest time in ms to fetch trades for
        :param int [limit]: the maximum number of trade structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict[]: a list of `trade structures <https://docs.ccxt.com/#/?id=trade-structure>`
        """
        await self.load_markets()
        messageHash = 'myTrades'
        market = None
        if symbol is not None:
            market = self.market(symbol)
            symbol = market['symbol']
            messageHash = messageHash + ':' + symbol
        type = None
        type, params = self.handle_market_type_and_params('watchMyTrades', market, params)
        trades = None
        if type == 'spot':
            channel = '<EMAIL>'
            trades = await self.watch_spot_private(channel, messageHash, params)
        else:
            trades = await self.watch_swap_private(messageHash, params)
        if self.newUpdates:
            limit = trades.getLimit(symbol, limit)
        return self.filter_by_symbol_since_limit(trades, symbol, since, limit, True)

    def handle_my_trade(self, client: Client, message, subscription=None):
        #
        #    {
        #        "c": "<EMAIL>",
        #        "d": {
        #            "p": "22339.99",
        #            "v": "0.000235",
        #            "S": 1,
        #            "T": 1678670940695,
        #            "t": "9f6a47fb926442e496c5c4c104076ae3",
        #            "c": '',
        #            "i": "e2b9835d1b6745f8a10ab74a81a16d50",
        #            "m": 0,
        #            "st": 0
        #        },
        #        "s": "BTCUSDT",
        #        "t": 1678670940700
        #    }
        #
        messageHash = 'myTrades'
        data = self.safe_value_2(message, 'd', 'data')
        futuresMarketId = self.safe_string(data, 'symbol')
        marketId = self.safe_string(message, 's', futuresMarketId)
        market = self.safe_market(marketId)
        symbol = market['symbol']
        trade = None
        if market['spot']:
            trade = self.parse_ws_trade(data, market)
        else:
            trade = self.parse_trade(data, market)
        trades = self.myTrades
        if trades is None:
            limit = self.safe_integer(self.options, 'tradesLimit', 1000)
            trades = ArrayCacheBySymbolById(limit)
            self.myTrades = trades
        trades.append(trade)
        client.resolve(trades, messageHash)
        symbolSpecificMessageHash = messageHash + ':' + symbol
        client.resolve(trades, symbolSpecificMessageHash)

    def parse_ws_trade(self, trade, market=None):
        #
        # public trade
        #    {
        #        "p": "20382.70",
        #        "v": "0.043800",
        #        "S": 1,
        #        "t": 1678593222456,
        #    }
        # private trade
        #    {
        #        "S": 1,
        #        "T": 1661938980268,
        #        "c": "",
        #        "i": "c079b0fcb80a46e8b128b281ce4e4f38",
        #        "m": 1,
        #        "p": "1.008",
        #        "st": 0,
        #        "t": "4079b1522a0b40e7919f609e1ea38d44",
        #        "v": "5"
        #    }
        #
        #
        #   d: {
        #       p: '1.0005',
        #       v: '5.71',
        #       a: '5.712855',
        #       S: 1,
        #       T: 1714325698237,
        #       t: 'edafcd9fdc2f426e82443d114691f724',
        #       c: '',
        #       i: 'C02__413321238354677760043',
        #       m: 0,
        #       st: 0,
        #       n: '0.005712855',
        #       N: 'USDT'
        #   }
        timestamp = self.safe_integer(trade, 'T')
        tradeId = self.safe_string(trade, 't')
        if timestamp is None:
            timestamp = self.safe_integer(trade, 't')
            tradeId = None
        priceString = self.safe_string(trade, 'p')
        amountString = self.safe_string(trade, 'v')
        rawSide = self.safe_string(trade, 'S')
        side = 'buy' if (rawSide == '1') else 'sell'
        isMaker = self.safe_integer(trade, 'm')
        feeAmount = self.safe_number(trade, 'n')
        feeCurrencyId = self.safe_string(trade, 'N')
        return self.safe_trade({
            'info': trade,
            'id': tradeId,
            'order': self.safe_string(trade, 'i'),
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'symbol': self.safe_symbol(None, market),
            'type': None,
            'side': side,
            'takerOrMaker': 'maker' if (isMaker) else 'taker',
            'price': priceString,
            'amount': amountString,
            'cost': None,
            'fee': {
                'cost': feeAmount,
                'currency': self.safe_currency_code(feeCurrencyId),
            },
        }, market)

    async def watch_orders(self, symbol: Str = None, since: Int = None, limit: Int = None, params={}) -> List[Order]:
        """
        :see: https://mxcdevelop.github.io/apidocs/spot_v3_en/#spot-account-orders
        :see: https://mxcdevelop.github.io/apidocs/spot_v3_en/#margin-account-orders
        watches information on multiple orders made by the user
        :param str symbol: unified market symbol of the market orders were made in
        :param int [since]: the earliest time in ms to fetch orders for
        :param int [limit]: the maximum number of order structures to retrieve
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :param str|None params['type']: the type of orders to retrieve, can be 'spot' or 'margin'
        :returns dict[]: a list of `order structures <https://docs.ccxt.com/#/?id=order-structure>`
        """
        await self.load_markets()
        params = self.omit(params, 'type')
        messageHash = 'orders'
        market = None
        if symbol is not None:
            market = self.market(symbol)
            symbol = market['symbol']
            messageHash = messageHash + ':' + symbol
        type = None
        type, params = self.handle_market_type_and_params('watchOrders', market, params)
        orders = None
        if type == 'spot':
            channel = type + '@private.orders.v3.api'
            orders = await self.watch_spot_private(channel, messageHash, params)
        else:
            orders = await self.watch_swap_private(messageHash, params)
        if self.newUpdates:
            limit = orders.getLimit(symbol, limit)
        return self.filter_by_symbol_since_limit(orders, symbol, since, limit, True)

    def handle_order(self, client: Client, message):
        #
        # spot
        #    {
        #        "c": "<EMAIL>",
        #        "d": {
        #              "A":8.0,
        #              "O":1661938138000,
        #              "S":1,
        #              "V":10,
        #              "a":8,
        #              "c":"",
        #              "i":"e03a5c7441e44ed899466a7140b71391",
        #              "m":0,
        #              "o":1,
        #              "p":0.8,
        #              "s":1,
        #              "v":10,
        #              "ap":0,
        #              "cv":0,
        #              "ca":0
        #        },
        #        "s": "MXUSDT",
        #        "t": 1661938138193
        #    }
        # spot - stop
        #    {
        #        "c": "<EMAIL>",
        #        "d": {
        #              "N":"USDT",
        #              "O":1661938853715,
        #              "P":0.9,
        #              "S":1,
        #              "T":"LE",
        #              "i":"f6d82e5f41d745f59fe9d3cafffd80b5",
        #              "o":100,
        #              "p":1.01,
        #              "s":"NEW",
        #              "v":6
        #        },
        #        "s": "MXUSDT",
        #        "t": 1661938853727
        #    }
        # margin
        #    {
        #        "c": "<EMAIL>",
        #        "d":{
        #             "O":1661938138000,
        #             "p":"0.8",
        #             "a":"8",
        #             "v":"10",
        #            "da":"0",
        #            "dv":"0",
        #             "A":"8.0",
        #             "V":"10",
        #             "n": "0",
        #             "N": "USDT",
        #             "S":1,
        #             "o":1,
        #             "s":1,
        #             "i":"e03a5c7441e44ed899466a7140b71391",
        #        },
        #        "s": "MXUSDT",
        #        "t":1661938138193
        #    }
        #
        messageHash = 'orders'
        data = self.safe_value_2(message, 'd', 'data')
        futuresMarketId = self.safe_string(data, 'symbol')
        marketId = self.safe_string(message, 's', futuresMarketId)
        market = self.safe_market(marketId)
        symbol = market['symbol']
        parsed = None
        if market['spot']:
            parsed = self.parse_ws_order(data, market)
        else:
            parsed = self.parse_order(data, market)
        orders = self.orders
        if orders is None:
            limit = self.safe_integer(self.options, 'ordersLimit', 1000)
            orders = ArrayCacheBySymbolById(limit)
            self.orders = orders
        orders.append(parsed)
        client.resolve(orders, messageHash)
        symbolSpecificMessageHash = messageHash + ':' + symbol
        client.resolve(orders, symbolSpecificMessageHash)

    def parse_ws_order(self, order, market=None):
        #
        # spot
        #     {
        #          "A":8.0,
        #          "O":1661938138000,
        #          "S":1,
        #          "V":10,
        #          "a":8,
        #          "c":"",
        #          "i":"e03a5c7441e44ed899466a7140b71391",
        #          "m":0,
        #          "o":1,
        #          "p":0.8,
        #          "s":1,
        #          "v":10,
        #          "ap":0,
        #          "cv":0,
        #          "ca":0
        #    }
        # spot - stop
        #    {
        #        "N":"USDT",
        #        "O":1661938853715,
        #        "P":0.9,
        #        "S":1,
        #        "T":"LE",
        #        "i":"f6d82e5f41d745f59fe9d3cafffd80b5",
        #        "o":100,
        #        "p":1.01,
        #        "s":"NEW",
        #        "v":6
        #    }
        # margin
        #    {
        #        "O":1661938138000,
        #        "p":"0.8",
        #        "a":"8",
        #        "v":"10",
        #       "da":"0",
        #       "dv":"0",
        #        "A":"8.0",
        #        "V":"10",
        #        "n": "0",
        #        "N": "USDT",
        #        "S":1,
        #        "o":1,
        #        "s":1,
        #        "i":"e03a5c7441e44ed899466a7140b71391",
        #    }
        #
        timestamp = self.safe_integer(order, 'O')
        side = self.safe_string(order, 'S')
        status = self.safe_string(order, 's')
        type = self.safe_string(order, 'o')
        fee = None
        feeCurrency = self.safe_string(order, 'N')
        if feeCurrency is not None:
            fee = {
                'currency': feeCurrency,
                'cost': None,
            }
        return self.safe_order({
            'id': self.safe_string(order, 'i'),
            'clientOrderId': self.safe_string(order, 'c'),
            'timestamp': timestamp,
            'datetime': self.iso8601(timestamp),
            'lastTradeTimestamp': None,
            'status': self.parse_ws_order_status(status, market),
            'symbol': self.safe_symbol(None, market),
            'type': self.parse_ws_order_type(type),
            'timeInForce': self.parse_ws_time_in_force(type),
            'side': 'buy' if (side == '1') else 'sell',
            'price': self.safe_string(order, 'p'),
            'stopPrice': None,
            'triggerPrice': self.safe_number(order, 'P'),
            'average': self.safe_string(order, 'ap'),
            'amount': self.safe_string(order, 'v'),
            'cost': self.safe_string(order, 'a'),
            'filled': self.safe_string(order, 'cv'),
            'remaining': self.safe_string(order, 'V'),
            'fee': fee,
            'trades': None,
            'info': order,
        }, market)

    def parse_ws_order_status(self, status, market=None):
        statuses: dict = {
            '1': 'open',     # new order
            '2': 'closed',   # filled
            '3': 'open',     # partially filled
            '4': 'canceled',  # canceled
            '5': 'open',     # order partially filled
            '6': 'closed',   # partially filled then canceled
            'NEW': 'open',
            'CANCELED': 'canceled',
            'EXECUTED': 'closed',
            'FAILED': 'rejected',
        }
        return self.safe_string(statuses, status, status)

    def parse_ws_order_type(self, type):
        types: dict = {
            '1': 'limit',   # LIMIT_ORDER
            '2': None,  # POST_ONLY
            '3': None,  # IMMEDIATE_OR_CANCEL
            '4': None,  # FILL_OR_KILL
            '5': 'market',  # MARKET_ORDER
            '100': 'limit',  # STOP_LIMIT
        }
        return self.safe_string(types, type)

    def parse_ws_time_in_force(self, timeInForce):
        timeInForceIds: dict = {
            '1': 'GTC',   # LIMIT_ORDER
            '2': 'PO',  # POST_ONLY
            '3': 'IOC',  # IMMEDIATE_OR_CANCEL
            '4': 'FOK',  # FILL_OR_KILL
            '5': 'GTC',  # MARKET_ORDER
            '100': 'GTC',  # STOP_LIMIT
        }
        return self.safe_string(timeInForceIds, timeInForce)

    async def watch_balance(self, params={}) -> Balances:
        """
        :see: https://mxcdevelop.github.io/apidocs/spot_v3_en/#spot-account-upadte
        watch balance and get the amount of funds available for trading or funds locked in orders
        :param dict [params]: extra parameters specific to the exchange API endpoint
        :returns dict: a `balance structure <https://docs.ccxt.com/#/?id=balance-structure>`
        """
        await self.load_markets()
        type = None
        type, params = self.handle_market_type_and_params('watchBalance', None, params)
        messageHash = 'balance:' + type
        if type == 'spot':
            channel = '<EMAIL>'
            return await self.watch_spot_private(channel, messageHash, params)
        else:
            return await self.watch_swap_private(messageHash, params)

    def handle_balance(self, client: Client, message):
        #
        # spot
        #    {
        #        "c": "<EMAIL>",
        #        "d": {
        #            "a": "USDT",
        #            "c": *************,
        #            "f": "302.185113007893322435",
        #            "fd": "-4.990689704",
        #            "l": "4.990689704",
        #            "ld": "4.990689704",
        #            "o": "ENTRUST_PLACE"
        #        },
        #        "t": 1678185928435
        #    }
        #
        #
        # swap balance
        #
        #     {
        #         "channel": "push.personal.asset",
        #         "data": {
        #             "availableBalance": 67.2426683348,
        #             "bonus": 0,
        #             "currency": "USDT",
        #             "frozenBalance": 0,
        #             "positionMargin": 1.36945756
        #         },
        #         "ts": 1680059188190
        #     }
        #
        c = self.safe_string(message, 'c')
        type = 'swap' if (c is None) else 'spot'
        messageHash = 'balance:' + type
        data = self.safe_value_2(message, 'd', 'data')
        futuresTimestamp = self.safe_integer(message, 'ts')
        timestamp = self.safe_integer(data, 'c', futuresTimestamp)
        if not (type in self.balance):
            self.balance[type] = {}
        self.balance[type]['info'] = data
        self.balance[type]['timestamp'] = timestamp
        self.balance[type]['datetime'] = self.iso8601(timestamp)
        currencyId = self.safe_string_2(data, 'a', 'currency')
        code = self.safe_currency_code(currencyId)
        account = self.account()
        account['free'] = self.safe_string_2(data, 'f', 'availableBalance')
        account['used'] = self.safe_string_2(data, 'l', 'frozenBalance')
        self.balance[type][code] = account
        self.balance[type] = self.safe_balance(self.balance[type])
        client.resolve(self.balance[type], messageHash)

    async def authenticate(self, subscriptionHash, params={}):
        # we only need one listenKey since ccxt shares connections
        listenKey = self.safe_string(self.options, 'listenKey')
        if listenKey is not None:
            return listenKey
        response = await self.spotPrivatePostUserDataStream(params)
        #
        #    {
        #        "listenKey": "pqia91ma19a5s61cv6a81va65sdf19v8a65a1a5s61cv6a81va65sdf19v8a65a1"
        #    }
        #
        listenKey = self.safe_string(response, 'listenKey')
        self.options['listenKey'] = listenKey
        listenKeyRefreshRate = self.safe_integer(self.options, 'listenKeyRefreshRate', 1200000)
        self.delay(listenKeyRefreshRate, self.keep_alive_listen_key, listenKey, params)
        return listenKey

    async def keep_alive_listen_key(self, listenKey, params={}):
        if listenKey is None:
            return
        request: dict = {
            'listenKey': listenKey,
        }
        try:
            await self.spotPrivatePutUserDataStream(self.extend(request, params))
            listenKeyRefreshRate = self.safe_integer(self.options, 'listenKeyRefreshRate', 1200000)
            self.delay(listenKeyRefreshRate, self.keep_alive_listen_key, listenKey, params)
        except Exception as error:
            url = self.urls['api']['ws']['spot'] + '?listenKey=' + listenKey
            client = self.client(url)
            self.options['listenKey'] = None
            client.reject(error)
            del self.clients[url]

    def handle_pong(self, client: Client, message):
        client.lastPong = self.milliseconds()
        return message

    def handle_subscription_status(self, client: Client, message):
        #
        #    {
        #        "id": 0,
        #        "code": 0,
        #        "msg": "<EMAIL>@BTCUSDT"
        #    }
        #
        msg = self.safe_string(message, 'msg')
        if msg == 'PONG':
            self.handle_pong(client, message)
        elif msg.find('@') > -1:
            parts = msg.split('@')
            channel = self.safe_string(parts, 1)
            methods: dict = {
                'public.increase.depth.v3.api': self.handle_order_book_subscription,
            }
            method = self.safe_value(methods, channel)
            if method is not None:
                method(client, message)

    def handle_message(self, client: Client, message):
        if isinstance(message, str):
            if message == 'Invalid listen key':
                error = AuthenticationError(self.id + ' invalid listen key')
                client.reject(error)
            return
        if 'msg' in message:
            self.handle_subscription_status(client, message)
            return
        c = self.safe_string(message, 'c')
        channel = None
        if c is None:
            channel = self.safe_string(message, 'channel')
        else:
            parts = c.split('@')
            channel = self.safe_string(parts, 1)
        methods: dict = {
            'public.deals.v3.api': self.handle_trades,
            'push.deal': self.handle_trades,
            'public.kline.v3.api': self.handle_ohlcv,
            'push.kline': self.handle_ohlcv,
            'public.bookTicker.v3.api': self.handle_ticker,
            'push.ticker': self.handle_ticker,
            'public.increase.depth.v3.api': self.handle_order_book,
            'push.depth': self.handle_order_book,
            'private.orders.v3.api': self.handle_order,
            'push.personal.order': self.handle_order,
            'private.account.v3.api': self.handle_balance,
            'push.personal.asset': self.handle_balance,
            'private.deals.v3.api': self.handle_my_trade,
            'push.personal.order.deal': self.handle_my_trade,
            'pong': self.handle_pong,
        }
        if channel in methods:
            method = methods[channel]
            method(client, message)

    def ping(self, client: Client):
        return {'method': 'ping'}
