# -*- coding: utf-8 -*-

# PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
# https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

from ccxt.async_support.htx import htx
from ccxt.abstract.huobi import ImplicitAPI


class huobi(htx, ImplicitAPI):

    def describe(self):
        return self.deep_extend(super(huobi, self).describe(), {
            'id': 'huobi',
            'alias': True,
        })
