#!/usr/bin/env python3
"""
Critical Quantum Validation Test
Independent validation of quantum functionality claims
"""

import asyncio
import sys
import time
import numpy as np
from pathlib import Path

sys.path.append('.')

def test_live_data_integration():
    """Test 1: Live Data Integration - Minimum 30+ cryptocurrencies"""
    print("🔍 CRITICAL TEST 1: Live Data Integration")
    print("=" * 60)
    
    try:
        from app.agents.scrapers.multi_source_fetcher import MultiSourceDataFetcher, DataSource
        
        async def fetch_data():
            fetcher = MultiSourceDataFetcher()
            
            # Test with higher limit to ensure we get 30+
            market_data, metadata = await fetcher.get_top_cryptocurrencies(limit=50)
            
            print(f"📊 Cryptocurrencies fetched: {market_data.height}")
            print(f"📈 Sources successful: {metadata['sources_successful']}")
            print(f"⏱️  Fetch time: {metadata['fetch_time']:.2f}s")
            
            if market_data.height >= 30:
                print("✅ PASSED: Live data integration works with 30+ cryptocurrencies")
                
                # Verify data quality
                sample = market_data.head(5)
                print("\n📋 Sample data verification:")
                for row in sample.iter_rows(named=True):
                    price = row.get('price', 0)
                    volume = row.get('volume_24h', 0)
                    print(f"  {row['symbol']}: ${price:.4f}, Vol: ${volume:,.0f}")
                
                return True, market_data
            else:
                print(f"❌ FAILED: Only {market_data.height} cryptocurrencies, need 30+")
                return False, None
        
        success, data = asyncio.run(fetch_data())
        return success, data
        
    except Exception as e:
        print(f"❌ CRITICAL FAILURE: Live data integration failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_quantum_circuit_functionality():
    """Test 2: Actual Quantum Circuit Implementation"""
    print("\n🔍 CRITICAL TEST 2: Quantum Circuit Functionality")
    print("=" * 60)
    
    try:
        from app.agents.quantum.quantum_score_calculator import QuantumScoreCalculator
        
        calculator = QuantumScoreCalculator(num_qubits=4, num_layers=2)
        
        # Test quantum circuit creation
        ansatz, observable = calculator.create_quantum_model()
        
        print(f"📊 Quantum ansatz parameters: {ansatz.num_parameters}")
        print(f"📊 Observable terms: {len(observable.paulis)}")
        print(f"📊 Circuit depth: {ansatz.depth()}")
        print(f"📊 Circuit gates: {len(ansatz.data)}")
        
        # Verify it's actually a quantum circuit
        if ansatz.num_parameters > 0 and len(observable.paulis) > 0:
            print("✅ PASSED: Quantum circuits are properly constructed")
            
            # Test with sample data
            sample_data = {
                'price': [100.0, 200.0, 50.0],
                'volume_24h': [1000000, 2000000, 500000],
                'price_change_percentage_24h': [5.0, -2.0, 10.0],
                'market_cap': [1e9, 2e9, 5e8],
                'market_cap_rank': [1, 2, 3]
            }
            
            import polars as pl
            df = pl.DataFrame(sample_data)
            
            result = calculator.calculate_quantum_score(df)
            print(f"📊 Quantum score calculation: {result}")
            
            return True
        else:
            print("❌ FAILED: Quantum circuits not properly constructed")
            return False
            
    except Exception as e:
        print(f"❌ CRITICAL FAILURE: Quantum circuit test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_quantum_training_verification():
    """Test 3: Verify Actual Quantum Training"""
    print("\n🔍 CRITICAL TEST 3: Quantum Training Verification")
    print("=" * 60)
    
    try:
        from app.agents.quantum.enhanced_training_pipeline import EnhancedQuantumTrainer, TrainingConfig, OptimizerType
        import polars as pl
        
        # Create minimal training data
        training_data = {
            'symbol': ['BTC', 'ETH', 'ADA', 'DOT', 'LINK', 'UNI'],
            'price': [50000, 3000, 1.5, 25, 15, 8],
            'volume_24h': [1e9, 5e8, 1e8, 5e7, 1e8, 2e8],
            'price_change_percentage_24h': [2.5, -1.0, 5.0, -3.0, 1.5, 4.0],
            'market_cap': [1e12, 3e11, 5e10, 2e10, 6e9, 4e9],
            'market_cap_rank': [1, 2, 3, 4, 5, 6]
        }
        
        df = pl.DataFrame(training_data)
        
        # Split data
        train_size = int(0.8 * len(df))
        val_size = int(0.1 * len(df))
        
        train_data = df.head(train_size)
        val_data = df.slice(train_size, val_size)
        test_data = df.tail(len(df) - train_size - val_size)
        
        print(f"📊 Training samples: {train_data.height}")
        print(f"📊 Validation samples: {val_data.height}")
        print(f"📊 Test samples: {test_data.height}")
        
        # Configure training
        config = TrainingConfig(
            max_iterations=5,  # Reduced for testing
            optimizer_type=OptimizerType.SPSA,
            num_qubits=4,
            num_layers=2,
            early_stopping_patience=3
        )
        
        trainer = EnhancedQuantumTrainer(config)
        
        print("🚀 Starting quantum training...")
        start_time = time.time()
        
        result = trainer.train_model(
            train_data=train_data,
            val_data=val_data,
            test_data=test_data,
            model_name="validation_test"
        )
        
        training_time = time.time() - start_time
        
        print(f"📊 Training completed in {training_time:.2f}s")
        print(f"📊 Success: {result.success}")
        print(f"📊 Best validation cost: {result.best_val_cost:.4f}")
        print(f"📊 Total iterations: {result.total_iterations}")
        print(f"📊 Convergence achieved: {result.convergence_achieved}")
        
        if result.success and result.best_parameters is not None:
            print("✅ PASSED: Quantum training completed successfully")
            print(f"📊 Trained parameters shape: {result.best_parameters.shape}")
            return True, result
        else:
            print("❌ FAILED: Quantum training did not complete successfully")
            return False, None
            
    except Exception as e:
        print(f"❌ CRITICAL FAILURE: Quantum training test failed: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_classical_fallback_detection():
    """Test 4: Detect if system is using classical fallback instead of quantum"""
    print("\n🔍 CRITICAL TEST 4: Classical Fallback Detection")
    print("=" * 60)
    
    try:
        from app.agents.quantum.quantum_score_calculator import QuantumScoreCalculator
        import polars as pl
        
        calculator = QuantumScoreCalculator()
        
        # Create test data
        test_data = pl.DataFrame({
            'price': [100.0],
            'volume_24h': [1000000],
            'price_change_percentage_24h': [5.0],
            'market_cap': [1e9],
            'market_cap_rank': [1]
        })
        
        # Test multiple times to check for consistency
        scores = []
        for i in range(5):
            result = calculator.calculate_quantum_score(test_data)
            scores.append(result['quantum_scores'][0])
        
        # Check if scores are identical (indicating classical fallback)
        scores_array = np.array(scores)
        score_variance = np.var(scores_array)
        
        print(f"📊 Scores across 5 runs: {scores}")
        print(f"📊 Score variance: {score_variance:.6f}")
        
        if score_variance < 1e-10:
            print("⚠️  WARNING: Scores are identical - likely using classical fallback")
            print("❌ FAILED: System appears to be using classical fallback, not quantum")
            return False
        else:
            print("✅ PASSED: Score variance indicates quantum computation")
            return True
            
    except Exception as e:
        print(f"❌ CRITICAL FAILURE: Classical fallback detection failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all critical validation tests"""
    print("🚀 CRITICAL QUANTUM SYSTEM VALIDATION")
    print("=" * 80)
    print("Independent validation by senior quantitative finance engineer")
    print("Testing claims against actual implementation")
    print("=" * 80)
    
    results = {}
    
    # Test 1: Live Data Integration
    results['live_data'], market_data = test_live_data_integration()
    
    # Test 2: Quantum Circuit Functionality
    results['quantum_circuits'] = test_quantum_circuit_functionality()
    
    # Test 3: Quantum Training
    results['quantum_training'], training_result = test_quantum_training_verification()
    
    # Test 4: Classical Fallback Detection
    results['no_classical_fallback'] = test_classical_fallback_detection()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 CRITICAL VALIDATION SUMMARY")
    print("=" * 80)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL CRITICAL TESTS PASSED")
        print("✅ System demonstrates genuine quantum functionality")
    else:
        print("⚠️  CRITICAL ISSUES DETECTED")
        print("❌ System may not be using genuine quantum computation")
    
    return results

if __name__ == "__main__":
    main()
