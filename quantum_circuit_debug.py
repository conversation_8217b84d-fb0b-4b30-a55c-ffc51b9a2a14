#!/usr/bin/env python3
"""
Debug quantum circuit execution to understand parameter sensitivity issue
"""

import sys
import numpy as np
from qiskit import QuantumCircuit
from qiskit.circuit.library import RealAmplitudes
from qiskit.quantum_info import SparsePauliOp
from qiskit_aer.primitives import EstimatorV2 as Estimator

sys.path.append('.')

def test_simple_quantum_circuit():
    """Test a simple quantum circuit with parameter variation"""
    print("🔍 DEBUGGING QUANTUM CIRCUIT PARAMETER SENSITIVITY")
    print("=" * 60)

    from qiskit.circuit import Parameter

    # Create simple parameterized circuit
    theta1 = Parameter('θ1')
    theta2 = Parameter('θ2')

    qc = QuantumCircuit(2)
    qc.ry(theta1, 0)  # Parameterized rotation
    qc.ry(theta2, 1)  # Parameterized rotation
    qc.cx(0, 1)  # Entangling gate
    
    # Create observable - test different observables
    observables_to_test = [
        SparsePauliOp.from_list([("ZI", 1.0)]),  # Z on first qubit
        SparsePauliOp.from_list([("IZ", 1.0)]),  # Z on second qubit
        SparsePauliOp.from_list([("ZZ", 1.0)]),  # ZZ correlation
        SparsePauliOp.from_list([("XX", 1.0)]),  # XX correlation
    ]

    for obs_idx, observable in enumerate(observables_to_test):
        print(f"\n📊 Testing observable {obs_idx + 1}: {observable}")
        expectation_values = []
    
    # Create estimator
    estimator = Estimator()
    
    print("📊 Testing parameter sensitivity...")

    # Test different parameter values
    test_params = [
        [0.0, 0.0],
        [np.pi/4, 0.0],
        [np.pi/2, 0.0],
        [np.pi, 0.0],
        [0.0, np.pi/2],
        [np.pi/2, np.pi/2]
    ]

    for i, params in enumerate(test_params):
        # Bind parameters
        param_dict = {theta1: params[0], theta2: params[1]}
        bound_circuit = qc.assign_parameters(param_dict)
        
        # Execute
        job = estimator.run([(bound_circuit, observable)])
        result = job.result()
        
        # Extract expectation value
        evs = result[0].data.evs
        try:
            if hasattr(evs, '__len__') and len(evs) > 0:
                exp_val = float(evs[0])
            elif hasattr(evs, 'item'):
                exp_val = evs.item()
            else:
                exp_val = float(evs)
        except (TypeError, IndexError):
            exp_val = float(evs) if np.isscalar(evs) else 0.0
        
            expectation_values.append(exp_val)
            print(f"    Parameters {params}: Expectation = {exp_val:.6f}")

        # Check variance for this observable
        variance = np.var(expectation_values)
        print(f"    Variance: {variance:.6f}")

        if variance > 1e-6:
            print(f"    ✅ PASSED: Observable {obs_idx + 1} shows parameter sensitivity")
            return True

    print("❌ FAILED: No observable shows parameter sensitivity")
    return False

def test_realamplitudes_circuit():
    """Test RealAmplitudes circuit specifically"""
    print("\n🔍 TESTING REALAMPLITUDES CIRCUIT")
    print("=" * 60)
    
    # Create RealAmplitudes circuit
    ansatz = RealAmplitudes(2, reps=1)
    observable = SparsePauliOp.from_list([("ZZ", 1.0)])
    
    estimator = Estimator()
    
    print(f"📊 RealAmplitudes parameters: {ansatz.num_parameters}")
    print(f"📊 Circuit depth: {ansatz.depth()}")
    
    # Test different parameter values
    expectation_values = []
    
    for i in range(5):
        # Random parameters
        params = np.random.uniform(0, 2*np.pi, ansatz.num_parameters)
        
        # Decompose the circuit to avoid instruction issues
        decomposed_circuit = ansatz.decompose()
        bound_circuit = decomposed_circuit.assign_parameters(params)
        
        # Execute
        job = estimator.run([(bound_circuit, observable)])
        result = job.result()
        
        # Extract expectation value
        evs = result[0].data.evs
        try:
            if hasattr(evs, '__len__') and len(evs) > 0:
                exp_val = float(evs[0])
            elif hasattr(evs, 'item'):
                exp_val = evs.item()
            else:
                exp_val = float(evs)
        except (TypeError, IndexError):
            exp_val = float(evs) if np.isscalar(evs) else 0.0
        
        expectation_values.append(exp_val)
        print(f"  Run {i+1}: Expectation = {exp_val:.6f}")
    
    # Check variance
    variance = np.var(expectation_values)
    print(f"\n📊 Expectation value variance: {variance:.6f}")
    
    if variance > 1e-6:
        print("✅ PASSED: RealAmplitudes circuit shows parameter sensitivity")
        return True
    else:
        print("❌ FAILED: RealAmplitudes circuit shows no parameter sensitivity")
        return False

def test_feature_encoding_impact():
    """Test if feature encoding affects parameter sensitivity"""
    print("\n🔍 TESTING FEATURE ENCODING IMPACT")
    print("=" * 60)
    
    from app.agents.quantum.quantum_feature_encoder import QuantumFeatureEncoder
    
    # Create feature encoder
    encoder = QuantumFeatureEncoder(num_qubits=2, encoding_type="amplitude")
    
    # Create test features
    test_features = np.array([[0.1, 0.2, 0.3, 0.4]])
    
    # Encode features
    feature_circuits = encoder.encode_features(test_features)
    feature_circuit = feature_circuits[0]
    
    print(f"📊 Feature circuit depth: {feature_circuit.depth()}")
    print(f"📊 Feature circuit gates: {len(feature_circuit.data)}")
    
    # Create ansatz
    ansatz = RealAmplitudes(2, reps=1)
    observable = SparsePauliOp.from_list([("ZZ", 1.0)])
    
    estimator = Estimator()
    
    expectation_values = []
    
    for i in range(5):
        # Random parameters
        params = np.random.uniform(0, 2*np.pi, ansatz.num_parameters)
        
        # Combine feature circuit with ansatz
        combined_circuit = feature_circuit.compose(ansatz)
        combined_circuit = combined_circuit.decompose()
        bound_circuit = combined_circuit.assign_parameters(params)
        
        # Execute
        job = estimator.run([(bound_circuit, observable)])
        result = job.result()
        
        # Extract expectation value
        evs = result[0].data.evs
        try:
            if hasattr(evs, '__len__') and len(evs) > 0:
                exp_val = float(evs[0])
            elif hasattr(evs, 'item'):
                exp_val = evs.item()
            else:
                exp_val = float(evs)
        except (TypeError, IndexError):
            exp_val = float(evs) if np.isscalar(evs) else 0.0
        
        expectation_values.append(exp_val)
        print(f"  Run {i+1}: Expectation = {exp_val:.6f}")
    
    # Check variance
    variance = np.var(expectation_values)
    print(f"\n📊 Expectation value variance: {variance:.6f}")
    
    if variance > 1e-6:
        print("✅ PASSED: Combined circuit shows parameter sensitivity")
        return True
    else:
        print("❌ FAILED: Combined circuit shows no parameter sensitivity")
        return False

def main():
    """Run all debug tests"""
    print("🚀 QUANTUM CIRCUIT DEBUG SESSION")
    print("=" * 80)
    
    results = []
    
    # Test 1: Simple circuit
    results.append(test_simple_quantum_circuit())
    
    # Test 2: RealAmplitudes circuit
    results.append(test_realamplitudes_circuit())
    
    # Test 3: Feature encoding impact
    results.append(test_feature_encoding_impact())
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 DEBUG SUMMARY")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    test_names = ["Simple Circuit", "RealAmplitudes Circuit", "Feature Encoding"]
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 QUANTUM CIRCUITS WORKING CORRECTLY")
        print("✅ Parameter sensitivity confirmed")
    else:
        print("⚠️  QUANTUM CIRCUIT ISSUES DETECTED")
        print("❌ Parameter sensitivity problems found")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
